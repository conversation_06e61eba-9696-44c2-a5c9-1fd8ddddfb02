import os
import datetime
import json
import pickle
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from flask import url_for, redirect, session, request, flash

# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

# Se modificar estes escopos, exclua o arquivo token.pickle
SCOPES = ['https://www.googleapis.com/auth/calendar']

# Usar as credenciais do ambiente
import os
from config import Config

# Caminho para o arquivo client_secret.json ou credenciais do ambiente
CLIENT_SECRET_FILE = 'client_secret_local.json'
if not os.path.exists(CLIENT_SECRET_FILE):
    CLIENT_SECRET_FILE = 'client_secret.json'

# Credenciais do ambiente
CLIENT_ID = Config.GOOGLE_CLIENT_ID
CLIENT_SECRET = Config.GOOGLE_CLIENT_SECRET
REDIRECT_URI = Config.GOOGLE_REDIRECT_URI
PROJECT_ID = Config.GOOGLE_PROJECT_ID

def get_credentials():
    """Obtém as credenciais de autenticação do Google.

    Retorna:
        Credenciais OAuth2 válidas ou None se não for possível obter credenciais.
    """
    creds = None
    # O arquivo token.pickle armazena os tokens de acesso e atualização do usuário
    if os.path.exists('token.pickle'):
        with open('token.pickle', 'rb') as token:
            creds = pickle.load(token)

    # Se não houver credenciais válidas disponíveis, o usuário faz login
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            # Para aplicações web, redirecionamos para a página de autenticação
            # Isso será tratado pela rota oauth2callback
            from flask import redirect, url_for

            # Verificar se o arquivo client_secret.json existe
            if os.path.exists(CLIENT_SECRET_FILE):
                # Usar o arquivo client_secret.json
                flow = InstalledAppFlow.from_client_secrets_file(
                    CLIENT_SECRET_FILE, SCOPES)
            else:
                # Usar as credenciais do ambiente
                from google_auth_oauthlib.flow import Flow
                client_config = {
                    "web": {
                        "client_id": CLIENT_ID,
                        "project_id": PROJECT_ID,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_secret": CLIENT_SECRET,
                        "redirect_uris": [REDIRECT_URI],
                        "javascript_origins": ["http://localhost:5000"]
                    }
                }
                flow = Flow.from_client_config(
                    client_config, SCOPES)

            # Usar a URI de redirecionamento configurada no .env ou gerar dinamicamente
            if REDIRECT_URI:
                flow.redirect_uri = REDIRECT_URI
            else:
                flow.redirect_uri = url_for('main.oauth2callback', _external=True)
            authorization_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true')

            # Armazenar o estado na sessão para verificação posterior
            session['state'] = state

            # Redirecionar para a página de autenticação do Google
            return redirect(authorization_url)

        # Salvar as credenciais para a próxima execução
        with open('token.pickle', 'wb') as token:
            pickle.dump(creds, token)

    return creds

def create_google_meet_event(summary, description, start_time, end_time, attendees=None):
    """Cria um evento no Google Calendar com um link do Google Meet.

    Args:
        summary: Título do evento.
        description: Descrição do evento.
        start_time: Horário de início (datetime).
        end_time: Horário de término (datetime).
        attendees: Lista de e-mails dos participantes.

    Returns:
        Link do Google Meet ou None se ocorrer um erro.
    """
    try:
        creds = get_credentials()

        # Se get_credentials() retornou um redirecionamento, retorne-o
        if hasattr(creds, 'status_code') and creds.status_code == 302:
            return creds

        service = build('calendar', 'v3', credentials=creds)

        # Configurar o evento
        event = {
            'summary': summary,
            'description': description,
            'start': {
                'dateTime': start_time.isoformat(),
                'timeZone': 'America/Sao_Paulo',
            },
            'end': {
                'dateTime': end_time.isoformat(),
                'timeZone': 'America/Sao_Paulo',
            },
            'conferenceData': {
                'createRequest': {
                    'requestId': f'sample-request-id-{datetime.datetime.now().timestamp()}',
                    'conferenceSolutionKey': {
                        'type': 'hangoutsMeet'
                    }
                }
            }
        }

        # Adicionar participantes, se fornecidos
        if attendees:
            event['attendees'] = [{'email': email} for email in attendees]

        # Criar o evento
        event = service.events().insert(
            calendarId='primary',
            body=event,
            conferenceDataVersion=1
        ).execute()

        # Retornar o link do Google Meet
        return event.get('hangoutLink')

    except HttpError as error:
        print(f'Ocorreu um erro: {error}')
        return None
    except Exception as e:
        print(f'Erro inesperado: {e}')
        return None

def oauth2callback():
    """Callback para o fluxo de autenticação OAuth2."""
    # Verificar o estado para proteção contra CSRF
    state = session.get('state')
    if state is None:
        return redirect(url_for('main.index'))

    # Verificar se o arquivo client_secret.json existe
    if os.path.exists(CLIENT_SECRET_FILE):
        # Usar o arquivo client_secret.json
        flow = InstalledAppFlow.from_client_secrets_file(
            CLIENT_SECRET_FILE, SCOPES)
    else:
        # Usar as credenciais do ambiente
        from google_auth_oauthlib.flow import Flow
        client_config = {
            "web": {
                "client_id": CLIENT_ID,
                "project_id": PROJECT_ID,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "client_secret": CLIENT_SECRET,
                "redirect_uris": [REDIRECT_URI],
                "javascript_origins": ["http://localhost:5000"]
            }
        }
        flow = Flow.from_client_config(
            client_config, SCOPES)

    # Usar a URI de redirecionamento configurada no .env ou gerar dinamicamente
    if REDIRECT_URI:
        flow.redirect_uri = REDIRECT_URI
    else:
        flow.redirect_uri = url_for('main.oauth2callback', _external=True)

    # Usar o código de autorização para obter credenciais
    authorization_response = request.url

    # Corrigir o problema de HTTPS em ambiente de desenvolvimento
    if authorization_response.startswith('http://'):
        authorization_response = authorization_response.replace('http://', 'https://')

    try:
        flow.fetch_token(authorization_response=authorization_response)
    except Exception as e:
        print(f"Erro ao obter token: {e}")
        # Tentar uma abordagem alternativa
        from urllib.parse import parse_qs, urlparse
        query = parse_qs(urlparse(request.url).query)
        code = query.get('code', [''])[0]
        state = query.get('state', [''])[0]

        if code and state:
            # Configurar manualmente o token
            import requests
            token_url = "https://oauth2.googleapis.com/token"
            token_data = {
                'code': code,
                'client_id': CLIENT_ID,
                'client_secret': CLIENT_SECRET,
                'redirect_uri': REDIRECT_URI,
                'grant_type': 'authorization_code'
            }

            token_response = requests.post(token_url, data=token_data)
            if token_response.status_code == 200:
                token_json = token_response.json()
                from google.oauth2.credentials import Credentials
                creds = Credentials(
                    token=token_json.get('access_token'),
                    refresh_token=token_json.get('refresh_token'),
                    token_uri=token_url,
                    client_id=CLIENT_ID,
                    client_secret=CLIENT_SECRET,
                    scopes=SCOPES
                )
                flow.credentials = creds
            else:
                flash(f'Erro ao obter token: {token_response.text}', 'error')
                return redirect(url_for('main.index'))

    # Salvar as credenciais
    creds = flow.credentials
    with open('token.pickle', 'wb') as token:
        pickle.dump(creds, token)

    flash('Autenticação com Google Calendar concluída com sucesso!', 'success')
    return redirect(url_for('main.index'))
