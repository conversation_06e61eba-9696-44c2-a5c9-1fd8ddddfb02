"""
Schedule Service - Single Responsibility Principle
Responsável pela lógica de negócio de horários/agenda
"""

from typing import List, Optional
from datetime import time, date
from app.models.schedule import Schedule
from app.repositories.schedule_repository import ScheduleRepository
from app.repositories.professional_repository import ProfessionalRepository


class ScheduleService:
    """
    Serviço de agenda/horários
    Responsabilidades:
    - Gerenciar horários de trabalho dos profissionais
    - Validar conflitos de horários
    - Gerar slots disponíveis
    """
    
    def __init__(self):
        self.schedule_repo = ScheduleRepository()
        self.professional_repo = ProfessionalRepository()
    
    def create_schedule(self, professional_id: int, day_of_week: int,
                       start_time: time, end_time: time, 
                       slot_duration: int = 60) -> Schedule:
        """
        Cria um novo horário de trabalho
        
        Args:
            professional_id: ID do profissional
            day_of_week: Dia da semana (0=Segunda, 6=Domingo)
            start_time: Hor<PERSON>rio de início
            end_time: Horário de fim
            slot_duration: Duração dos slots em minutos
            
        Returns:
            Schedule object criado
            
        Raises:
            ValueError: Se dados inválidos ou conflito de horário
        """
        # Validar se profissional existe
        professional = self.professional_repo.get_by_id(professional_id)
        if not professional:
            raise ValueError("Profissional não encontrado")
        
        # Verificar conflitos
        if self.schedule_repo.has_conflicts(professional_id, day_of_week, start_time, end_time):
            raise ValueError("Já existe um horário configurado que conflita com este")
        
        # Criar horário
        return self.schedule_repo.create(
            professional_id=professional_id,
            day_of_week=day_of_week,
            start_time=start_time,
            end_time=end_time,
            slot_duration=slot_duration,
            is_active=True
        )
    
    def update_schedule(self, schedule_id: int, start_time: time = None,
                       end_time: time = None, slot_duration: int = None) -> bool:
        """Atualiza um horário existente"""
        schedule = self.schedule_repo.get_by_id(schedule_id)
        if not schedule:
            return False
        
        # Verificar conflitos se horários foram alterados
        if start_time or end_time:
            new_start = start_time or schedule.start_time
            new_end = end_time or schedule.end_time
            
            if self.schedule_repo.has_conflicts(
                schedule.professional_id, 
                schedule.day_of_week, 
                new_start, 
                new_end, 
                schedule.id
            ):
                raise ValueError("Horário conflita com outro horário existente")
        
        # Atualizar
        update_data = {}
        if start_time:
            update_data['start_time'] = start_time
        if end_time:
            update_data['end_time'] = end_time
        if slot_duration:
            update_data['slot_duration'] = slot_duration
        
        if update_data:
            self.schedule_repo.update(schedule, **update_data)
        
        return True
    
    def delete_schedule(self, schedule_id: int) -> bool:
        """Remove um horário"""
        schedule = self.schedule_repo.get_by_id(schedule_id)
        if not schedule:
            return False
        
        self.schedule_repo.delete(schedule)
        return True
    
    def get_professional_schedules(self, professional_id: int) -> List[Schedule]:
        """Retorna todos os horários de um profissional"""
        return self.schedule_repo.get_by_professional_id(professional_id)
    
    def get_available_slots_for_date(self, professional_id: int, target_date: date) -> List[dict]:
        """Retorna slots disponíveis para uma data específica"""
        return self.schedule_repo.get_available_slots_for_date(professional_id, target_date)
    
    def activate_schedule(self, schedule_id: int) -> bool:
        """Ativa um horário"""
        schedule = self.schedule_repo.get_by_id(schedule_id)
        if not schedule:
            return False
        
        schedule.activate()
        return True
    
    def deactivate_schedule(self, schedule_id: int) -> bool:
        """Desativa um horário"""
        schedule = self.schedule_repo.get_by_id(schedule_id)
        if not schedule:
            return False
        
        schedule.deactivate()
        return True
