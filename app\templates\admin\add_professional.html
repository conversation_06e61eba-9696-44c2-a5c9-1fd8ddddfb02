{% extends "base.html" %}

{% block title %}Add Professional - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Adicionar Profissional</h4>
                <a href="{{ url_for('main.admin_dashboard') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.specialty.label(class="form-label") }}
                        {{ form.specialty(class="form-control") }}
                        {% for error in form.specialty.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.license_number.label(class="form-label") }}
                        {{ form.license_number(class="form-control") }}
                        {% for error in form.license_number.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {{ form.phone(class="form-control") }}
                        {% for error in form.phone.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
