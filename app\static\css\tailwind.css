@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-gray-50 text-gray-900 font-sans antialiased;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-bold tracking-tight;
  }
  h1 {
    @apply text-4xl md:text-5xl;
  }
  h2 {
    @apply text-3xl md:text-4xl;
  }
  h3 {
    @apply text-2xl md:text-3xl;
  }
  h4 {
    @apply text-xl md:text-2xl;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-lg font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  .btn-accent {
    @apply bg-accent-600 text-white hover:bg-accent-700 focus:ring-accent-500;
  }
  .btn-outline {
    @apply border-2 bg-transparent shadow-none hover:shadow-sm;
  }
  .btn-outline-primary {
    @apply border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  .btn-lg {
    @apply px-6 py-3 text-lg;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-soft overflow-hidden transition-all duration-300 hover:shadow-card;
  }
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
  }
  .card-body {
    @apply p-6;
  }
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200;
  }
  
  .form-control {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  .form-error {
    @apply mt-1 text-sm text-red-600;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }
  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }
  .badge-success {
    @apply bg-green-100 text-green-800;
  }
  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .alert {
    @apply p-4 rounded-lg mb-4;
  }
  .alert-info {
    @apply bg-blue-50 text-blue-700 border-l-4 border-blue-500;
  }
  .alert-success {
    @apply bg-green-50 text-green-700 border-l-4 border-green-500;
  }
  .alert-warning {
    @apply bg-yellow-50 text-yellow-700 border-l-4 border-yellow-500;
  }
  .alert-danger {
    @apply bg-red-50 text-red-700 border-l-4 border-red-500;
  }
  
  .navbar-item {
    @apply px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200;
  }
  .navbar-item-light {
    @apply text-gray-100 hover:text-white hover:bg-white/10;
  }
  .navbar-item-dark {
    @apply text-gray-700 hover:text-gray-900 hover:bg-gray-100;
  }
  
  .table-container {
    @apply overflow-x-auto rounded-lg shadow-sm border border-gray-200;
  }
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  .table thead {
    @apply bg-gray-50;
  }
  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
  }
  .table tbody {
    @apply bg-white divide-y divide-gray-200;
  }
  .table-hover tr {
    @apply transition-colors duration-200;
  }
  .table-hover tr:hover {
    @apply bg-gray-50;
  }
  
  .calendar-day {
    @apply p-2 rounded-lg border border-gray-200 text-center cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50;
  }
  .calendar-day.active {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600;
  }
  .calendar-day.disabled {
    @apply bg-gray-100 text-gray-400 cursor-not-allowed hover:border-gray-200 hover:bg-gray-100;
  }
  
  .time-slot {
    @apply p-2 rounded-lg border border-gray-200 text-center cursor-pointer transition-all duration-200 hover:border-primary-500 hover:bg-primary-50;
  }
  .time-slot.active {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600;
  }
  .time-slot.disabled {
    @apply bg-gray-100 text-gray-400 cursor-not-allowed hover:border-gray-200 hover:bg-gray-100;
  }
}
