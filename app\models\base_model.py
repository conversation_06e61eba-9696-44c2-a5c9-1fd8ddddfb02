"""
Base Model - Single Responsibility Principle
Classe base que define comportamentos comuns para todas as entidades
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from app import db


class BaseModel(db.Model):
    """
    Classe base para todos os modelos
    Implementa comportamentos comuns seguindo DRY principle
    """
    __abstract__ = True
    
    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def save(self):
        """Salva a entidade no banco de dados"""
        db.session.add(self)
        db.session.commit()
        return self
    
    def delete(self):
        """Remove a entidade do banco de dados"""
        db.session.delete(self)
        db.session.commit()
    
    def update(self, **kwargs):
        """Atualiza os atributos da entidade"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()
        db.session.commit()
        return self
    
    def to_dict(self):
        """Converte a entidade para dicionário"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def __repr__(self):
        return f"<{self.__class__.__name__} {self.id}>"
