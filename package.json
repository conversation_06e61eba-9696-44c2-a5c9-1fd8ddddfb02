{"name": "gestao_terapia", "version": "1.0.0", "description": "Sistema web em Python para gestão de terapeutas e pacientes, com funcionalidades de agendamento e integração com Google Meet.", "main": "tailwind.config.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:css": "tailwindcss -i ./app/static/css/tailwind.css -o ./app/static/css/main.css", "watch:css": "tailwindcss -i ./app/static/css/tailwind.css -o ./app/static/css/main.css --watch"}, "repository": {"type": "git", "url": "git+https://github.com/matheusparreira22/gestao_terapia.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/matheusparreira22/gestao_terapia/issues"}, "homepage": "https://github.com/matheusparreira22/gestao_terapia#readme", "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "tailwindcss": "^4.1.7"}}