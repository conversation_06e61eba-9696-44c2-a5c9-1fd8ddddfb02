{% extends "base.html" %}

{% block title %}Schedule Appointment - {{ super() }}{% endblock %}

{% block styles %}
<style>
    .time-slot {
        cursor: pointer;
        transition: all 0.3s;
    }
    .time-slot:hover {
        background-color: #e9ecef;
    }
    .time-slot.selected {
        background-color: #cfe2ff;
        border-color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Agendar Consulta</h4>
                <a href="{{ url_for('main.professional_appointments') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-5">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="mb-0">1. Selecione uma Data</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" id="searchForm">
                                    {{ search_form.hidden_tag() }}
                                    <div class="mb-3">
                                        {{ search_form.date.label(class="form-label") }}
                                        {{ search_form.date(class="form-control", type="date") }}
                                        {% for error in search_form.date.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    <div class="d-grid">
                                        {{ search_form.submit(class="btn btn-primary") }}
                                    </div>
                                </form>
                            </div>
                        </div>

                        {% if available_slots %}
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">2. Selecione um Horário</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group">
                                        {% for slot in available_slots %}
                                            <div class="list-group-item time-slot" 
                                                 data-start="{{ slot.start_time }}" 
                                                 data-end="{{ slot.end_time }}">
                                                {{ slot.formatted }}
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <div class="col-md-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">3. Complete os Detalhes</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" id="appointmentForm">
                                    {{ form.hidden_tag() }}
                                    <div class="mb-3">
                                        {{ form.patient_id.label(class="form-label") }}
                                        {{ form.patient_id(class="form-select") }}
                                        {% for error in form.patient_id.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    <div class="mb-3">
                                        {{ form.appointment_date.label(class="form-label") }}
                                        {{ form.appointment_date(class="form-control", type="date", value=selected_date) }}
                                        {% for error in form.appointment_date.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Horário Selecionado</label>
                                        <div id="selectedTimeDisplay" class="form-control">Selecione um horário disponível</div>
                                        {{ form.start_time() }}
                                        {{ form.end_time() }}
                                    </div>
                                    <div class="mb-3">
                                        {{ form.notes.label(class="form-label") }}
                                        {{ form.notes(class="form-control", rows=3) }}
                                        {% for error in form.notes.errors %}
                                            <div class="text-danger">{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                    <div class="d-grid">
                                        {{ form.submit(class="btn btn-success", id="submitBtn", disabled="disabled") }}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preencher a data do formulário de agendamento com a data selecionada
        {% if selected_date %}
            document.getElementById('appointment_date').value = '{{ selected_date }}';
        {% endif %}
        
        // Manipular a seleção de horário
        const timeSlots = document.querySelectorAll('.time-slot');
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');
        const selectedTimeDisplay = document.getElementById('selectedTimeDisplay');
        const submitBtn = document.getElementById('submitBtn');
        
        timeSlots.forEach(slot => {
            slot.addEventListener('click', function() {
                // Remover seleção anterior
                timeSlots.forEach(s => s.classList.remove('selected'));
                
                // Adicionar seleção ao slot atual
                this.classList.add('selected');
                
                // Preencher os campos ocultos
                startTimeInput.value = this.dataset.start;
                endTimeInput.value = this.dataset.end;
                
                // Atualizar o display
                selectedTimeDisplay.textContent = `${this.dataset.start} - ${this.dataset.end}`;
                
                // Habilitar o botão de envio
                submitBtn.disabled = false;
            });
        });
    });
</script>
{% endblock %}
