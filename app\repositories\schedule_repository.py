"""
Schedule Repository - Single Responsibility Principle
Responsável pelo acesso aos dados de horários
"""

from typing import List
from datetime import date, time
from app.models.schedule import Schedule
from .base_repository import BaseRepository
from app import db


class ScheduleRepository(BaseRepository):
    """Repositório para operações específicas de horários"""

    def __init__(self):
        super().__init__(Schedule)

    def get_by_professional_id(self, professional_id: int) -> List[Schedule]:
        """Busca horários por profissional"""
        return self.find_by(professional_id=professional_id, is_active=True)

    def get_by_day_of_week(self, professional_id: int, day_of_week: int) -> List[Schedule]:
        """Busca horários por dia da semana"""
        return self.find_by(
            professional_id=professional_id,
            day_of_week=day_of_week,
            is_active=True
        )

    def get_available_slots_for_date(self, professional_id: int, target_date: date) -> List[dict]:
        """Retorna slots disponíveis para uma data"""
        return Schedule.get_available_slots_for_date(professional_id, target_date)

    def has_conflicts(self, professional_id: int, day_of_week: int,
                     start_time, end_time, exclude_id: int = None) -> bool:
        """Verifica conflitos de horário"""
        query = Schedule.query.filter_by(
            professional_id=professional_id,
            day_of_week=day_of_week,
            is_active=True
        )

        if exclude_id:
            query = query.filter(Schedule.id != exclude_id)

        conflicts = query.filter(
            db.or_(
                db.and_(Schedule.start_time <= start_time, Schedule.end_time > start_time),
                db.and_(Schedule.start_time < end_time, Schedule.end_time >= end_time),
                db.and_(Schedule.start_time >= start_time, Schedule.end_time <= end_time)
            )
        ).all()

        return len(conflicts) > 0

    def get_available_slots_for_date(self, professional_id: int, target_date: date) -> List[dict]:
        """Retorna slots disponíveis para uma data específica"""
        from app.repositories.appointment_repository import AppointmentRepository
        from datetime import datetime, timedelta

        # Buscar horários do profissional para o dia da semana
        day_of_week = target_date.weekday()  # 0=Monday, 6=Sunday
        schedules = self.find_by(professional_id=professional_id, day_of_week=day_of_week, is_active=True)

        if not schedules:
            return []

        # Buscar agendamentos existentes para a data
        appointment_repo = AppointmentRepository()
        existing_appointments = appointment_repo.get_by_professional_and_date(professional_id, target_date)

        available_slots = []

        for schedule in schedules:
            # Gerar slots baseado na duração configurada
            current_time = datetime.combine(target_date, schedule.start_time)
            end_time = datetime.combine(target_date, schedule.end_time)
            slot_duration = timedelta(minutes=schedule.slot_duration)

            while current_time + slot_duration <= end_time:
                slot_start = current_time.time()
                slot_end = (current_time + slot_duration).time()

                # Verificar se não há conflito com agendamentos existentes
                has_conflict = any(
                    self._times_overlap(slot_start, slot_end, apt.start_time, apt.end_time)
                    for apt in existing_appointments
                    if apt.status in ['scheduled', 'confirmed']  # Apenas agendamentos ativos
                )

                # Verificar se não é no passado (se for hoje)
                is_future = True
                if target_date == date.today():
                    current_datetime = datetime.now()
                    slot_datetime = datetime.combine(target_date, slot_start)
                    is_future = slot_datetime > current_datetime

                if not has_conflict and is_future:
                    available_slots.append({
                        'start_time': slot_start,
                        'end_time': slot_end,
                        'available': True,
                        'schedule_id': schedule.id
                    })

                current_time += slot_duration

        return available_slots

    def _times_overlap(self, start1: time, end1: time, start2: time, end2: time) -> bool:
        """Verifica se dois períodos de tempo se sobrepõem"""
        return start1 < end2 and end1 > start2

    def get_repository_specific_methods(self):
        return [
            'get_by_professional_id', 'get_by_day_of_week',
            'get_available_slots_for_date', 'has_conflicts'
        ]
