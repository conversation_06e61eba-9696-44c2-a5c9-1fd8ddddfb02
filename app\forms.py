from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON>F<PERSON>, PasswordField, SubmitField, SelectField, TextAreaField, DateField, TimeField, HiddenField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from app.models import User

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Sign In')


class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Repeat Password', validators=[DataRequired(), EqualTo('password')])
    role = SelectField('Role', choices=[('admin', 'Administrator'), ('professional', 'Professional')])
    submit = SubmitField('Register')

    def validate_username(self, username):
        user = User.get_by_username(username.data)
        if user is not None:
            raise ValidationError('Please use a different username.')

    def validate_email(self, email):
        user = User.get_by_email(email.data)
        if user is not None:
            raise ValidationError('Please use a different email address.')


class PatientRegistrationForm(FlaskForm):
    username = StringField('Nome de Usuário', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Senha', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Confirmar Senha', validators=[DataRequired(), EqualTo('password')])
    name = StringField('Nome Completo', validators=[DataRequired(), Length(max=100)])
    phone = StringField('Telefone', validators=[DataRequired(), Length(max=20)])
    date_of_birth = DateField('Data de Nascimento', validators=[DataRequired()])
    address = StringField('Endereço', validators=[DataRequired(), Length(max=200)])
    submit = SubmitField('Registrar')

    def validate_username(self, username):
        user = User.get_by_username(username.data)
        if user is not None:
            raise ValidationError('Este nome de usuário já está em uso. Por favor, escolha outro.')

    def validate_email(self, email):
        user = User.get_by_email(email.data)
        if user is not None:
            raise ValidationError('Este email já está em uso. Por favor, use outro endereço de email.')


class ProfessionalForm(FlaskForm):
    name = StringField('Full Name', validators=[DataRequired(), Length(max=100)])
    specialty = StringField('Specialty', validators=[DataRequired(), Length(max=100)])
    license_number = StringField('License Number', validators=[DataRequired(), Length(max=50)])
    phone = StringField('Phone Number', validators=[DataRequired(), Length(max=20)])
    submit = SubmitField('Save Professional')


class PatientForm(FlaskForm):
    name = StringField('Full Name', validators=[DataRequired(), Length(max=100)])
    email = StringField('Email', validators=[Email(), Length(max=120)])
    phone = StringField('Phone Number', validators=[DataRequired(), Length(max=20)])
    date_of_birth = DateField('Date of Birth', validators=[DataRequired()])
    address = StringField('Address', validators=[DataRequired(), Length(max=200)])
    notes = TextAreaField('Notes')
    submit = SubmitField('Save Patient')


class ScheduleForm(FlaskForm):
    day_of_week = SelectField('Day of Week', choices=[
        (0, 'Segunda-feira'),
        (1, 'Terça-feira'),
        (2, 'Quarta-feira'),
        (3, 'Quinta-feira'),
        (4, 'Sexta-feira'),
        (5, 'Sábado'),
        (6, 'Domingo')
    ], coerce=int, validators=[DataRequired()])
    start_time = StringField('Start Time (HH:MM)', validators=[DataRequired()])
    end_time = StringField('End Time (HH:MM)', validators=[DataRequired()])
    submit = SubmitField('Save Schedule')


class AppointmentForm(FlaskForm):
    patient_id = SelectField('Patient', coerce=int, validators=[DataRequired()])
    appointment_date = DateField('Date', validators=[DataRequired()])
    start_time = HiddenField('Start Time', validators=[DataRequired()])
    end_time = HiddenField('End Time', validators=[DataRequired()])
    notes = TextAreaField('Notes')
    submit = SubmitField('Schedule Appointment')


class AppointmentSearchForm(FlaskForm):
    date = DateField('Select Date', validators=[DataRequired()])
    submit = SubmitField('Search Available Slots')


class PatientAppointmentForm(FlaskForm):
    appointment_date = HiddenField('Date', validators=[DataRequired()])
    start_time = HiddenField('Start Time', validators=[DataRequired()])
    end_time = HiddenField('End Time', validators=[DataRequired()])
    notes = TextAreaField('Notes')
    submit = SubmitField('Confirm Appointment')


class PostAppointmentNotesForm(FlaskForm):
    post_appointment_notes = TextAreaField('Observações da Consulta', validators=[DataRequired()])
    submit = SubmitField('Salvar Observações')


class AppointmentFilterForm(FlaskForm):
    start_date = DateField('Data Inicial', format='%Y-%m-%d')
    end_date = DateField('Data Final', format='%Y-%m-%d')
    patient_id = SelectField('Paciente', coerce=int, choices=[], validators=[Optional()])
    status = SelectField('Status', choices=[
        ('all', 'Todos'),
        ('scheduled', 'Agendado'),
        ('completed', 'Concluído'),
        ('cancelled', 'Cancelado')
    ], default='all')
    submit = SubmitField('Filtrar')
