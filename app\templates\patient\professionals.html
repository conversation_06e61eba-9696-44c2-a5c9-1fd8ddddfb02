{% extends "base.html" %}

{% block title %}Profissionais Disponíveis - Sistema de Gestão Terapêutica{% endblock %}

{% block content %}
<div class="mb-4">
    <h2><i class="fas fa-user-md me-2"></i>Profissionais Disponíveis</h2>
    <p class="lead">Encontre um profissional e veja sua disponibilidade para agendamento.</p>
</div>

<div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
    {% for professional in professionals %}
        <div class="col">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-user-md text-primary me-2"></i>
                        {{ professional.name }}
                    </h5>
                    <h6 class="card-subtitle mb-2 text-muted">{{ professional.specialty }}</h6>

                    <div class="mb-3">
                        <p class="card-text mb-1">
                            <i class="fas fa-id-card me-2"></i>
                            <strong>Registro:</strong> {{ professional.license_number }}
                        </p>
                        <p class="card-text mb-1">
                            <i class="fas fa-phone me-2"></i>
                            <strong>Contato:</strong> {{ professional.phone }}
                        </p>
                        {% if professional.bio %}
                            <p class="card-text">
                                <i class="fas fa-info-circle me-2"></i>
                                {{ professional.bio[:100] }}{% if professional.bio|length > 100 %}...{% endif %}
                            </p>
                        {% endif %}
                    </div>

                    <div class="mb-2">
                        {% if professional.is_accepting_patients %}
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Aceitando pacientes
                            </span>
                        {% else %}
                            <span class="badge bg-warning">
                                <i class="fas fa-pause me-1"></i>Não aceitando pacientes
                            </span>
                        {% endif %}
                    </div>
                </div>

                <div class="card-footer">
                    {% if professional.is_accepting_patients %}
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('patient.professional_schedule', professional_id=professional.id) }}" class="btn btn-outline-primary">
                                <i class="fas fa-calendar-alt me-2"></i>Ver Agenda
                            </a>
                            <a href="{{ url_for('patient.book_appointment', professional_id=professional.id) }}" class="btn btn-primary">
                                <i class="fas fa-calendar-plus me-2"></i>Agendar Consulta
                            </a>
                        </div>
                    {% else %}
                        <button class="btn btn-secondary w-100" disabled>
                            <i class="fas fa-ban me-2"></i>Indisponível
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    {% else %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Nenhum profissional disponível no momento.</strong>
                <p class="mb-0 mt-2">Tente novamente mais tarde ou entre em contato com a administração.</p>
            </div>
        </div>
    {% endfor %}
</div>

{% if professionals %}
    <div class="mt-4">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    Como funciona?
                </h5>
                <p class="card-text">
                    Escolha um profissional disponível e clique em "Ver Agenda" para visualizar os horários disponíveis
                    e agendar sua consulta. Você receberá um link do Google Meet para a consulta online.
                </p>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
