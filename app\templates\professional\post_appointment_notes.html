{% extends "base.html" %}

{% block title %}Adicionar <PERSON> da Consulta - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Adicionar O<PERSON>er<PERSON>ç<PERSON> da Consulta</h4>
                <a href="{{ url_for('main.professional_appointments') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h5>Detalhes da Consulta</h5>
                    <p class="mb-1"><strong>Paciente:</strong> {{ patient.name }}</p>
                    <p class="mb-1"><strong>Data:</strong> {{ appointment.appointment_date }}</p>
                    <p class="mb-0"><strong><PERSON><PERSON><PERSON><PERSON>:</strong> {{ appointment.start_time }} - {{ appointment.end_time }}</p>
                </div>
                
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.post_appointment_notes.label(class="form-label") }}
                        {{ form.post_appointment_notes(class="form-control", rows=10, placeholder="Registre aqui suas observações sobre a consulta, diagnósticos, tratamentos recomendados, etc...") }}
                        {% for error in form.post_appointment_notes.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="alert alert-warning mb-0">
                    <strong>Importante:</strong> Estas observações são confidenciais e só podem ser visualizadas por você. Elas não serão compartilhadas diretamente com o paciente.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
