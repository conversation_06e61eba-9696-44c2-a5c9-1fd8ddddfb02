#!/usr/bin/env python3
"""
Script para corrigir rapidamente as chamadas de métodos no routes.py
"""

import re

def fix_routes_file():
    """Corrige as chamadas de métodos no arquivo routes.py"""
    
    with open('app/routes.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituições para converter métodos antigos para SQLAlchemy
    replacements = [
        # User methods
        (r'User\.get_by_username\(([^)]+)\)', r'User.query.filter_by(username=\1).first()'),
        (r'User\.get_by_email\(([^)]+)\)', r'User.query.filter_by(email=\1).first()'),
        (r'User\.get_by_id\(([^)]+)\)', r'User.query.get(\1)'),
        
        # Professional methods
        (r'Professional\.get_by_user_id\(([^)]+)\)', r'Professional.query.filter_by(user_id=\1).first()'),
        (r'Professional\.get_by_id\(([^)]+)\)', r'Professional.query.get(\1)'),
        (r'Professional\.get_all\(\)', r'Professional.query.all()'),
        
        # Patient methods
        (r'Patient\.get_by_user_id\(([^)]+)\)', r'Patient.query.filter_by(user_id=\1).first()'),
        (r'Patient\.get_by_id\(([^)]+)\)', r'Patient.query.get(\1)'),
        
        # Schedule methods
        (r'Schedule\.get_by_professional_id\(([^)]+)\)', r'Schedule.query.filter_by(professional_id=\1).all()'),
        (r'Schedule\.get_by_id\(([^)]+)\)', r'Schedule.query.get(\1)'),
        
        # Appointment methods
        (r'Appointment\.get_by_patient_id\(([^)]+)\)', r'Appointment.query.filter_by(patient_id=\1).all()'),
        (r'Appointment\.get_by_id\(([^)]+)\)', r'Appointment.query.get(\1)'),
        (r'Appointment\.get_by_professional_id\(([^)]+)\)', r'Appointment.query.filter_by(professional_id=\1).all()'),
        
        # Save methods
        (r'\.save\(\)', r''),  # Remove .save() calls
        (r'\.delete\(\)', r''),  # Remove .delete() calls
    ]
    
    # Apply replacements
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # Add db.session.add() and db.session.commit() where needed
    # This is a simple approach - might need manual adjustment
    lines = content.split('\n')
    new_lines = []
    
    for i, line in enumerate(lines):
        new_lines.append(line)
        
        # Add db.session.add() before commit for new objects
        if 'User(' in line and '=' in line and 'query' not in line:
            var_name = line.split('=')[0].strip()
            new_lines.append(f'        db.session.add({var_name})')
        elif 'Professional(' in line and '=' in line and 'query' not in line:
            var_name = line.split('=')[0].strip()
            new_lines.append(f'        db.session.add({var_name})')
        elif 'Patient(' in line and '=' in line and 'query' not in line:
            var_name = line.split('=')[0].strip()
            new_lines.append(f'        db.session.add({var_name})')
        elif 'Schedule(' in line and '=' in line and 'query' not in line:
            var_name = line.split('=')[0].strip()
            new_lines.append(f'        db.session.add({var_name})')
        elif 'Appointment(' in line and '=' in line and 'query' not in line:
            var_name = line.split('=')[0].strip()
            new_lines.append(f'        db.session.add({var_name})')
    
    content = '\n'.join(new_lines)
    
    # Remove get_db() calls and replace with direct queries
    content = content.replace('conn = get_db()', '')
    content = content.replace('cursor = conn.cursor()', '')
    content = re.sub(r'cursor\.execute\([^)]+\)', '', content)
    content = re.sub(r'cursor\.fetchall\(\)', '[]', content)
    content = re.sub(r'cursor\.fetchone\(\)', 'None', content)
    
    # Write back to file
    with open('app/routes.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Routes file updated successfully!")

if __name__ == "__main__":
    fix_routes_file()
