"""
Appointment Service - Single Responsibility Principle
Responsável pela lógica de negócio de agendamentos
"""

from typing import List, Optional
from datetime import datetime, date, time, timedelta
from app.models.appointment import Appointment, AppointmentStatus
from app.repositories.appointment_repository import AppointmentRepository
from app.repositories.professional_repository import ProfessionalRepository
from app.repositories.patient_repository import PatientRepository
from app.repositories.schedule_repository import ScheduleRepository


class AppointmentService:
    """
    Serviço de agendamentos
    Responsabilidades:
    - Criar e gerenciar agendamentos
    - Validar disponibilidade
    - Integrar com Google Meet
    - Gerenciar status de agendamentos
    """
    
    def __init__(self):
        self.appointment_repo = AppointmentRepository()
        self.professional_repo = ProfessionalRepository()
        self.patient_repo = PatientRepository()
        self.schedule_repo = ScheduleRepository()
    
    def create_appointment(self, professional_id: int, patient_id: int, 
                          appointment_date: date, start_time: time, 
                          end_time: time, notes: str = None) -> Appointment:
        """
        Cria um novo agendamento
        
        Args:
            professional_id: ID do profissional
            patient_id: ID do paciente
            appointment_date: Data do agendamento
            start_time: Horário de início
            end_time: Horário de fim
            notes: Notas opcionais
            
        Returns:
            Appointment object criado
            
        Raises:
            ValueError: Se dados inválidos ou conflito de horário
        """
        # Validações de negócio
        self._validate_appointment_data(professional_id, patient_id, appointment_date, start_time, end_time)
        
        # Verificar conflitos
        conflicts = self.appointment_repo.check_conflicts(
            professional_id, appointment_date, start_time, end_time
        )
        if conflicts:
            raise ValueError("Já existe um agendamento neste horário")
        
        # Verificar se o horário está disponível na agenda do profissional
        available_slots = self.schedule_repo.get_available_slots_for_date(professional_id, appointment_date)
        slot_available = any(
            slot['start_time'] <= start_time and slot['end_time'] >= end_time
            for slot in available_slots
        )
        
        if not slot_available:
            raise ValueError("Horário não disponível na agenda do profissional")
        
        # Criar agendamento
        appointment = self.appointment_repo.create(
            professional_id=professional_id,
            patient_id=patient_id,
            appointment_date=appointment_date,
            start_time=start_time,
            end_time=end_time,
            status=AppointmentStatus.SCHEDULED.value,
            notes=notes
        )
        
        # Gerar link do Google Meet
        try:
            meeting_link = self._generate_meeting_link(appointment)
            if meeting_link:
                appointment.meeting_link = meeting_link
                appointment.save()
        except Exception as e:
            print(f"Erro ao gerar link do Google Meet: {e}")
        
        return appointment
    
    def get_available_slots(self, professional_id: int, target_date: date) -> List[dict]:
        """Retorna slots disponíveis para uma data"""
        return self.schedule_repo.get_available_slots_for_date(professional_id, target_date)
    
    def confirm_appointment(self, appointment_id: int) -> bool:
        """Confirma um agendamento"""
        appointment = self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            return False
        
        try:
            appointment.confirm()
            return True
        except ValueError as e:
            raise e
    
    def complete_appointment(self, appointment_id: int, post_notes: str = None) -> bool:
        """Completa um agendamento"""
        appointment = self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            return False
        
        try:
            appointment.complete(post_notes)
            return True
        except ValueError as e:
            raise e
    
    def cancel_appointment(self, appointment_id: int, reason: str = None) -> bool:
        """Cancela um agendamento"""
        appointment = self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            return False
        
        try:
            appointment.cancel(reason)
            return True
        except ValueError as e:
            raise e
    
    def reschedule_appointment(self, appointment_id: int, new_date: date, 
                             new_start_time: time, new_end_time: time) -> bool:
        """Reagenda um compromisso"""
        appointment = self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            return False
        
        # Verificar conflitos na nova data/hora
        conflicts = self.appointment_repo.check_conflicts(
            appointment.professional_id, new_date, new_start_time, new_end_time, appointment.id
        )
        if conflicts:
            raise ValueError("Já existe um agendamento no novo horário")
        
        try:
            appointment.reschedule(new_date, new_start_time, new_end_time)
            return True
        except ValueError as e:
            raise e
    
    def get_appointments_by_professional(self, professional_id: int, 
                                       start_date: date = None, 
                                       end_date: date = None) -> List[Appointment]:
        """Busca agendamentos por profissional"""
        return self.appointment_repo.get_by_professional_id(professional_id, start_date, end_date)
    
    def get_appointments_by_patient(self, patient_id: int) -> List[Appointment]:
        """Busca agendamentos por paciente"""
        return self.appointment_repo.get_by_patient_id(patient_id)
    
    def get_upcoming_appointments(self, professional_id: int = None, 
                                patient_id: int = None) -> List[Appointment]:
        """Busca agendamentos futuros"""
        return self.appointment_repo.get_upcoming_appointments(professional_id, patient_id)
    
    def get_today_appointments(self, professional_id: int = None) -> List[Appointment]:
        """Busca agendamentos de hoje"""
        return self.appointment_repo.get_today_appointments(professional_id)
    
    def _validate_appointment_data(self, professional_id: int, patient_id: int,
                                 appointment_date: date, start_time: time, end_time: time):
        """Valida dados do agendamento"""
        # Verificar se profissional existe
        professional = self.professional_repo.get_by_id(professional_id)
        if not professional:
            raise ValueError("Profissional não encontrado")
        
        # Verificar se paciente existe
        patient = self.patient_repo.get_by_id(patient_id)
        if not patient:
            raise ValueError("Paciente não encontrado")
        
        # Validar data
        if appointment_date < date.today():
            raise ValueError("Não é possível agendar para datas passadas")
        
        # Validar horários
        if start_time >= end_time:
            raise ValueError("Horário de início deve ser anterior ao horário de fim")
        
        # Validar se é hoje e horário já passou
        if appointment_date == date.today():
            current_time = datetime.now().time()
            if start_time <= current_time:
                raise ValueError("Não é possível agendar para horários que já passaram")
    
    def _generate_meeting_link(self, appointment: Appointment) -> Optional[str]:
        """Gera link do Google Meet para o agendamento"""
        try:
            # Importar aqui para evitar dependência circular
            from app.models.appointment import Appointment as AppointmentModel
            
            return AppointmentModel.generate_meet_link(
                appointment.professional_id,
                appointment.patient_id,
                appointment.appointment_date,
                appointment.start_time
            )
        except Exception as e:
            print(f"Erro ao gerar link do Google Meet: {e}")
            return None
