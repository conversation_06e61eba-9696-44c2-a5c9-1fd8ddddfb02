#!/usr/bin/env python3
"""
Script de seed para popular o banco de dados com dados de teste
"""

import sqlite3
import hashlib
from werkzeug.security import generate_password_hash

def seed_database():
    """Popula o banco de dados com dados de teste"""
    
    # Conectar ao banco de dados
    conn = sqlite3.connect('app.db')
    cursor = conn.cursor()
    
    print("🌱 Iniciando seed do banco de dados...")
    
    try:
        # 1. Criar usuário para o profissional com email específico
        email = "<EMAIL>"
        username = "matheus_parreira"
        password_hash = generate_password_hash("teste123")
        
        # Verificar se o usuário já existe
        cursor.execute('SELECT id FROM user WHERE email = ?', (email,))
        existing_user = cursor.fetchone()
        
        if existing_user:
            user_id = existing_user[0]
            print(f"✅ Usuário já existe: {username}")
        else:
            # Inserir usuário
            cursor.execute('''
                INSERT INTO user (username, email, password_hash, role)
                VALUES (?, ?, ?, ?)
            ''', (username, email, password_hash, 'professional'))
            user_id = cursor.lastrowid
            print(f"✅ Usuário criado: {username}")
        
        # 2. Criar profissional
        cursor.execute('SELECT id FROM professional WHERE user_id = ?', (user_id,))
        existing_professional = cursor.fetchone()
        
        if existing_professional:
            professional_id = existing_professional[0]
            print(f"✅ Profissional já existe")
        else:
            cursor.execute('''
                INSERT INTO professional (user_id, name, specialty, license_number, phone)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, "Dr. Matheus Parreira", "Psicologia Clínica", "CRP-12345", "(11) 99999-9999"))
            professional_id = cursor.lastrowid
            print(f"✅ Profissional criado: Dr. Matheus Parreira")
        
        # 3. Criar horários de agenda (Segunda a Sexta)
        schedules = [
            # Segunda-feira (0)
            (professional_id, 0, "08:00", "12:00"),
            (professional_id, 0, "14:00", "18:00"),
            # Terça-feira (1)
            (professional_id, 1, "08:00", "12:00"),
            (professional_id, 1, "14:00", "18:00"),
            # Quarta-feira (2)
            (professional_id, 2, "08:00", "12:00"),
            (professional_id, 2, "14:00", "18:00"),
            # Quinta-feira (3)
            (professional_id, 3, "08:00", "12:00"),
            (professional_id, 3, "14:00", "18:00"),
            # Sexta-feira (4)
            (professional_id, 4, "08:00", "12:00"),
            (professional_id, 4, "14:00", "17:00"),
        ]
        
        # Verificar se já existem horários
        cursor.execute('SELECT COUNT(*) FROM schedule WHERE professional_id = ?', (professional_id,))
        existing_schedules = cursor.fetchone()[0]
        
        if existing_schedules > 0:
            print(f"✅ Horários já existem: {existing_schedules} horários")
        else:
            cursor.executemany('''
                INSERT INTO schedule (professional_id, day_of_week, start_time, end_time)
                VALUES (?, ?, ?, ?)
            ''', schedules)
            print(f"✅ {len(schedules)} horários de agenda criados")
        
        # 4. Criar alguns pacientes de exemplo
        patients = [
            (professional_id, "Ana Silva", "<EMAIL>", "(11) 98888-8888", "1990-05-15", "Rua das Flores, 123", "Paciente com ansiedade", None),
            (professional_id, "João Santos", "<EMAIL>", "(11) 97777-7777", "1985-10-20", "Av. Principal, 456", "Paciente com depressão", None),
            (professional_id, "Maria Oliveira", "<EMAIL>", "(11) 96666-6666", "1992-03-08", "Rua da Paz, 789", "Terapia de casal", None),
        ]
        
        # Verificar se já existem pacientes
        cursor.execute('SELECT COUNT(*) FROM patient WHERE professional_id = ?', (professional_id,))
        existing_patients = cursor.fetchone()[0]
        
        if existing_patients > 0:
            print(f"✅ Pacientes já existem: {existing_patients} pacientes")
        else:
            cursor.executemany('''
                INSERT INTO patient (professional_id, name, email, phone, date_of_birth, address, notes, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', patients)
            print(f"✅ {len(patients)} pacientes de exemplo criados")
        
        # Commit das mudanças
        conn.commit()
        
        print("\n🎉 Seed concluído com sucesso!")
        print("\n📝 Credenciais do profissional criado:")
        print(f"   📧 Email: {email}")
        print(f"   👤 Username: {username}")
        print(f"   🔑 Senha: teste123")
        print(f"   👨‍⚕️ Nome: Dr. Matheus Parreira")
        print(f"   🏥 Especialidade: Psicologia Clínica")
        
        print("\n📅 Horários de atendimento:")
        print("   Segunda a Quinta: 08:00-12:00 e 14:00-18:00")
        print("   Sexta-feira: 08:00-12:00 e 14:00-17:00")
        
        print("\n👥 Pacientes de exemplo criados:")
        print("   • Ana Silva")
        print("   • João Santos") 
        print("   • Maria Oliveira")
        
        print("\n🌐 Para testar:")
        print("   1. Acesse: http://127.0.0.1:5000/login")
        print("   2. Faça login com as credenciais acima")
        print("   3. Explore o dashboard do profissional")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o seed: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    success = seed_database()
    if not success:
        print("\n❌ Falha no seed do banco de dados!")
        exit(1)
