# Sistema de Gestão de Terapia

Sistema web em Python para gestão de terapeutas e pacientes, com funcionalidades de agendamento e integração com Google Meet.

## Funcionalidades

- Cadastro de profissionais e pacientes
- Agendamento de consultas
- Integração com Google Calendar e Google Meet
- Registro de informações sobre consultas realizadas
- Visualização de histórico de consultas

## Requisitos

- Python 3.8+
- Flask
- SQLite
- Google API Client

## Configuração do Ambiente

1. Clone o repositório:
```bash
git clone <url-do-repositorio>
cd gestao_terapia
```

2. Crie um ambiente virtual e ative-o:
```bash
python -m venv venv
# No Windows
venv\Scripts\activate
# No Linux/Mac
source venv/bin/activate
```

3. Instale as dependências:
```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:
```bash
# Copie o arquivo de exemplo
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

5. Configure as credenciais do Google OAuth:
   - Acesse o [Google Cloud Console](https://console.cloud.google.com/)
   - Crie um projeto e habilite as APIs do Google Calendar
   - Crie credenciais OAuth2 para aplicação web
   - Configure a URI de redirecionamento como `http://localhost:5000/oauth2callback`
   - Baixe o arquivo JSON de credenciais e salve como `client_secret.json` na raiz do projeto

6. Execute a aplicação:
```bash
python run.py
```

7. Acesse a aplicação em `http://localhost:5000`

## Usuários de Teste

A aplicação cria automaticamente os seguintes usuários para teste:

- **Administrador**:
  - Username: admin
  - Senha: admin123

- **Profissional**:
  - Username: profissional
  - Senha: prof123

- **Paciente**:
  - Username: paciente
  - Senha: paciente123

## Arquivos Sensíveis

Os seguintes arquivos contêm informações sensíveis e não devem ser commitados:

- `.env`: Contém variáveis de ambiente e configurações sensíveis
- `client_secret.json`: Contém credenciais do Google OAuth
- `token.pickle`: Contém tokens de acesso e atualização para a API do Google

Estes arquivos estão incluídos no `.gitignore` e não devem ser adicionados ao repositório.

## Desenvolvimento

Para desenvolvimento local com OAuth2, a aplicação está configurada para permitir HTTP em vez de HTTPS. Em produção, você deve usar HTTPS para garantir a segurança.

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.
