#!/usr/bin/env python3
"""
Script para verificar se o usuário foi criado corretamente
"""

import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models import User, Professional, init_db

def check_user():
    """Verifica se o usuário foi criado"""
    
    # Inicializar o banco de dados
    init_db()
    
    # Buscar por email
    email = "<EMAIL>"
    user_by_email = User.get_by_email(email)
    
    # Buscar por username
    username = "matheus_parreira"
    user_by_username = User.get_by_username(username)
    
    print(f"🔍 Buscando usuário...")
    print(f"📧 Por email ({email}): {'✅ Encontrado' if user_by_email else '❌ Não encontrado'}")
    print(f"👤 Por username ({username}): {'✅ Encontrado' if user_by_username else '❌ Não encontrado'}")
    
    if user_by_email:
        print(f"\n📋 Dados do usuário (por email):")
        print(f"   ID: {user_by_email.id}")
        print(f"   Username: {user_by_email.username}")
        print(f"   Email: {user_by_email.email}")
        print(f"   Role: {user_by_email.role}")
        
        # Verificar profissional
        professional = Professional.get_by_user_id(user_by_email.id)
        if professional:
            print(f"\n👨‍⚕️ Profissional associado:")
            print(f"   ID: {professional.id}")
            print(f"   Nome: {professional.name}")
            print(f"   Especialidade: {professional.specialty}")
        else:
            print(f"\n❌ Nenhum profissional associado")
    
    if user_by_username and user_by_username != user_by_email:
        print(f"\n📋 Dados do usuário (por username):")
        print(f"   ID: {user_by_username.id}")
        print(f"   Username: {user_by_username.username}")
        print(f"   Email: {user_by_username.email}")
        print(f"   Role: {user_by_username.role}")

if __name__ == "__main__":
    check_user()
