"""
User Model - Single Responsibility Principle
Entidade de domínio responsável apenas pela representação do usuário
"""

from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
from .base_model import BaseModel


class User(UserMixin, BaseModel):
    """
    Entidade User - representa um usuário do sistema
    Responsabilidades:
    - Autenticação e autorização
    - Gerenciamento de senha
    - Roles do sistema
    """
    __tablename__ = 'users'
    
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='patient')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Relacionamentos (definidos aqui para manter a integridade referencial)
    professional = db.relationship('Professional', backref='user', uselist=False, cascade='all, delete-orphan')
    patient = db.relationship('Patient', backref='user', uselist=False, cascade='all, delete-orphan')
    
    def set_password(self, password: str) -> None:
        """Define a senha do usuário de forma segura"""
        if not password or len(password) < 6:
            raise ValueError("Password must be at least 6 characters long")
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """Verifica se a senha está correta"""
        if not password:
            return False
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self) -> bool:
        """Verifica se o usuário é administrador"""
        return self.role == 'admin'
    
    def is_professional(self) -> bool:
        """Verifica se o usuário é profissional"""
        return self.role == 'professional'
    
    def is_patient(self) -> bool:
        """Verifica se o usuário é paciente"""
        return self.role == 'patient'
    
    def get_profile(self):
        """Retorna o perfil específico do usuário baseado no role"""
        if self.is_professional():
            return self.professional
        elif self.is_patient():
            return self.patient
        return None
    
    def activate(self) -> None:
        """Ativa o usuário"""
        self.is_active = True
        self.save()
    
    def deactivate(self) -> None:
        """Desativa o usuário"""
        self.is_active = False
        self.save()
    
    def change_role(self, new_role: str) -> None:
        """Altera o role do usuário"""
        valid_roles = ['admin', 'professional', 'patient']
        if new_role not in valid_roles:
            raise ValueError(f"Invalid role. Must be one of: {valid_roles}")
        self.role = new_role
        self.save()
    
    def to_dict(self):
        """Converte para dicionário sem expor dados sensíveis"""
        data = super().to_dict()
        data.pop('password_hash', None)  # Remove senha do dicionário
        return data
    
    def __repr__(self):
        return f'<User {self.username} ({self.role})>'
