{% extends "base.html" %}

{% block title %}Consultas Realizadas - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Consultas Realizadas</h2>
    <a href="{{ url_for('main.professional_appointments') }}" class="btn btn-outline-secondary">Voltar para Agendamentos</a>
</div>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Histórico de Consultas</h5>
        <span class="badge bg-primary">{{ consultations|length }} consulta(s) realizada(s)</span>
    </div>
    <div class="card-body">
        {% if consultations %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th><PERSON><PERSON><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Observações</th>
                            <th><PERSON><PERSON><PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for consultation in consultations %}
                            <tr>
                                <td>{{ consultation.appointment.appointment_date }}</td>
                                <td>{{ consultation.appointment.start_time }} - {{ consultation.appointment.end_time }}</td>
                                <td>
                                    <a href="{{ url_for('main.view_patient', id=consultation.patient.id) }}">
                                        {{ consultation.patient.name }}
                                    </a>
                                </td>
                                <td>
                                    {% if consultation.appointment.post_appointment_notes %}
                                        <span class="text-success">
                                            <i class="bi bi-check-circle-fill"></i> Registradas
                                        </span>
                                    {% else %}
                                        <span class="text-warning">
                                            <i class="bi bi-exclamation-circle-fill"></i> Pendentes
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#consultationModal{{ consultation.appointment.id }}">
                                            Ver Detalhes
                                        </button>
                                        <a href="{{ url_for('main.add_post_appointment_notes', id=consultation.appointment.id) }}" class="btn btn-outline-info">
                                            {% if consultation.appointment.post_appointment_notes %}Editar Observações{% else %}Adicionar Observações{% endif %}
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                Nenhuma consulta realizada encontrada. As consultas aparecerão aqui após serem marcadas como concluídas.
            </div>
        {% endif %}
    </div>
</div>

<!-- Modais de Detalhes das Consultas -->
{% for consultation in consultations %}
    <div class="modal fade" id="consultationModal{{ consultation.appointment.id }}" tabindex="-1" aria-labelledby="consultationModalLabel{{ consultation.appointment.id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="consultationModalLabel{{ consultation.appointment.id }}">
                        Detalhes da Consulta - {{ consultation.patient.name }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>Informações da Consulta</h6>
                            <p><strong>Data:</strong> {{ consultation.appointment.appointment_date }}</p>
                            <p><strong>Horário:</strong> {{ consultation.appointment.start_time }} - {{ consultation.appointment.end_time }}</p>
                            <p><strong>Status:</strong> Concluída</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Informações do Paciente</h6>
                            <p><strong>Nome:</strong> {{ consultation.patient.name }}</p>
                            <p><strong>Email:</strong> {{ consultation.patient.email }}</p>
                            <p><strong>Telefone:</strong> {{ consultation.patient.phone }}</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Notas Pré-Consulta</h6>
                        <div class="p-3 bg-light rounded">
                            {% if consultation.appointment.notes %}
                                {{ consultation.appointment.notes|nl2br }}
                            {% else %}
                                <em>Nenhuma nota pré-consulta registrada.</em>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Observações da Consulta</h6>
                        <div class="p-3 bg-light rounded">
                            {% if consultation.appointment.post_appointment_notes %}
                                {{ consultation.appointment.post_appointment_notes|nl2br }}
                            {% else %}
                                <em>Nenhuma observação registrada para esta consulta.</em>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <a href="{{ url_for('main.add_post_appointment_notes', id=consultation.appointment.id) }}" class="btn btn-primary">
                        {% if consultation.appointment.post_appointment_notes %}Editar Observações{% else %}Adicionar Observações{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endfor %}

<div class="alert alert-info">
    <h5>Dicas:</h5>
    <ul>
        <li>Clique em "Ver Detalhes" para visualizar todas as informações da consulta.</li>
        <li>Clique em "Adicionar Observações" para registrar suas observações sobre a consulta.</li>
        <li>Clique no nome do paciente para ver o histórico completo de consultas desse paciente.</li>
    </ul>
</div>
{% endblock %}
