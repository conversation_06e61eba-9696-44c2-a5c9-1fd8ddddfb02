{% extends "base.html" %}

{% block title %}Dashboard do Paciente - {{ super() }}{% endblock %}

{% block content %}
<div class="mb-4">
    <h2>Meu Dashboard</h2>
    <p class="lead">Be<PERSON>-vindo(a), {{ patient.name }}!</p>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Meus Dados</h5>
            </div>
            <div class="card-body">
                <p><strong>Nome:</strong> {{ patient.name }}</p>
                <p><strong>Email:</strong> {{ patient.email }}</p>
                <p><strong>Telefone:</strong> {{ patient.phone }}</p>
                <p><strong>Data de Nascimento:</strong> {{ patient.date_of_birth }}</p>
                <p><strong>Endereço:</strong> {{ patient.address }}</p>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary">Buscar Profissionais</a>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Próximas Consultas</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Horário</th>
                                    <th>Profissional</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    {% set professional = appointment.get_professional() %}
                                    <tr>
                                        <td>{{ appointment.appointment_date }}</td>
                                        <td>{{ appointment.start_time }} - {{ appointment.end_time }}</td>
                                        <td>{{ professional.name }}</td>
                                        <td>
                                            {% if appointment.status == 'scheduled' %}
                                                <span class="badge bg-primary">Agendado</span>
                                            {% elif appointment.status == 'completed' %}
                                                <span class="badge bg-success">Concluído</span>
                                            {% elif appointment.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelado</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if appointment.status == 'scheduled' %}
                                                    {% if appointment.meeting_link %}
                                                        <a href="{{ appointment.meeting_link }}" target="_blank" class="btn btn-sm btn-outline-primary">Entrar na Reunião</a>
                                                    {% endif %}
                                                    <a href="{{ url_for('main.patient_cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('Tem certeza que deseja cancelar este agendamento?')">Cancelar</a>
                                                {% else %}
                                                    <button class="btn btn-sm btn-outline-secondary" disabled>Finalizado</button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        Você não tem consultas agendadas. <a href="{{ url_for('patient.professionals') }}">Agende uma consulta</a>.
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Histórico de Consultas</h5>
            </div>
            <div class="card-body">
                {% if past_appointments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Horário</th>
                                    <th>Profissional</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments %}
                                    {% set professional = appointment.get_professional() %}
                                    <tr>
                                        <td>{{ appointment.appointment_date }}</td>
                                        <td>{{ appointment.start_time }} - {{ appointment.end_time }}</td>
                                        <td>{{ professional.name }}</td>
                                        <td>
                                            {% if appointment.status == 'scheduled' %}
                                                <span class="badge bg-primary">Agendado</span>
                                            {% elif appointment.status == 'completed' %}
                                                <span class="badge bg-success">Concluído</span>
                                            {% elif appointment.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelado</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        Você ainda não tem histórico de consultas.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
