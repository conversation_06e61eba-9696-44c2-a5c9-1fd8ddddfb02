{% extends "base.html" %}

{% block title %}Dashboard do Paciente - {{ super() }}{% endblock %}

{% block content %}
<div class="mb-4">
    <h2>Meu Dashboard</h2>
    <p class="lead">Be<PERSON>-vindo(a), {{ patient.name }}!</p>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Meus Dados</h5>
            </div>
            <div class="card-body">
                <p><strong>Nome:</strong> {{ patient.name }}</p>
                <p><strong>Email:</strong> {{ patient.email }}</p>
                <p><strong>Telefone:</strong> {{ patient.phone }}</p>
                <p><strong>Data de Nascimento:</strong> {{ patient.date_of_birth }}</p>
                <p><strong>Endereço:</strong> {{ patient.address }}</p>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary">
                        <i class="fas fa-user-md me-2"></i>Buscar Profissionais
                    </a>
                    <a href="{{ url_for('patient.appointments') }}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar-check me-2"></i>Meus Agendamentos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Próximas Consultas</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Horário</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.appointment_date.strftime('%d/%m/%Y') if appointment.appointment_date else '-' }}</td>
                                        <td>{{ appointment.start_time.strftime('%H:%M') if appointment.start_time else '-' }} - {{ appointment.end_time.strftime('%H:%M') if appointment.end_time else '-' }}</td>
                                        <td>
                                            {% if appointment.status == 'scheduled' %}
                                                <span class="badge bg-primary">Agendado</span>
                                            {% elif appointment.status == 'confirmed' %}
                                                <span class="badge bg-success">Confirmado</span>
                                            {% elif appointment.status == 'completed' %}
                                                <span class="badge bg-info">Concluído</span>
                                            {% elif appointment.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelado</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ appointment.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if appointment.status in ['scheduled', 'confirmed'] %}
                                                    {% if appointment.meeting_link %}
                                                        <a href="{{ appointment.meeting_link }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-video"></i> Entrar
                                                        </a>
                                                    {% endif %}
                                                    <button class="btn btn-sm btn-outline-danger" onclick="alert('Funcionalidade em desenvolvimento')">
                                                        <i class="fas fa-times"></i> Cancelar
                                                    </button>
                                                {% else %}
                                                    <button class="btn btn-sm btn-outline-secondary" disabled>Finalizado</button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-calendar-times fa-2x mb-2"></i>
                        <p class="mb-2">Você não tem consultas agendadas.</p>
                        <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary">
                            <i class="fas fa-calendar-plus me-2"></i>Agendar Consulta
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Ações Rápidas</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <a href="{{ url_for('patient.professionals') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-md me-2"></i>Buscar Profissionais
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ url_for('patient.appointments') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-calendar-check me-2"></i>Ver Todos os Agendamentos
                        </a>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Dica:</strong> Você pode agendar consultas diretamente com os profissionais disponíveis.
                        Todas as consultas incluem link do Google Meet automaticamente.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
