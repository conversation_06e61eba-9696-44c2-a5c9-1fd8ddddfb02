@echo off
REM Script para facilitar o desenvolvimento com Docker no Windows

setlocal enabledelayedexpansion

REM Verificar se Docker está instalado
where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker não está instalado. Por favor, instale o Docker primeiro.
    exit /b 1
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro.
    exit /b 1
)

REM Processar argumentos
if "%1"=="" goto help
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="logs" goto logs
if "%1"=="shell" goto shell
if "%1"=="exec" goto exec_cmd
if "%1"=="migrate" goto migrate
if "%1"=="create-migration" goto create_migration
if "%1"=="reset-db" goto reset_db
if "%1"=="help" goto help
if "%1"=="--help" goto help
if "%1"=="-h" goto help

echo [ERROR] Comando desconhecido: %1
echo.
goto help

:start
echo ================================
echo Iniciando Serviços Docker
echo ================================
echo.
echo [INFO] Parando containers existentes...
docker-compose down
echo.
echo [INFO] Construindo e iniciando containers...
docker-compose up --build -d
echo.
echo [INFO] Aguardando banco de dados ficar pronto...
timeout /t 10 /nobreak >nul
echo.
echo [INFO] Verificando status dos containers...
docker-compose ps
echo.
echo [INFO] Serviços iniciados com sucesso!
echo.
echo 🌐 Aplicação: http://localhost:5000
echo 🗄️  Adminer (DB Admin): http://localhost:8080
echo 📊 PostgreSQL: localhost:5432
echo.
echo Credenciais do banco:
echo   Database: gestao_terapia
echo   Username: postgres
echo   Password: postgres123
goto end

:stop
echo ================================
echo Parando Serviços Docker
echo ================================
docker-compose down
echo [INFO] Serviços parados com sucesso!
goto end

:restart
call :stop
call :start
goto end

:logs
echo ================================
echo Visualizando Logs
echo ================================
docker-compose logs -f
goto end

:shell
echo [INFO] Acessando shell do container da aplicação...
docker-compose exec web /bin/bash
goto end

:exec_cmd
echo [INFO] Executando comando no container da aplicação...
shift
docker-compose exec web %*
goto end

:migrate
echo ================================
echo Executando Migrações do Banco
echo ================================
docker-compose exec web flask db upgrade
goto end

:create_migration
if "%2"=="" (
    echo [ERROR] Por favor, forneça uma mensagem para a migração.
    echo Uso: %0 create-migration "mensagem da migração"
    exit /b 1
)
echo ================================
echo Criando Nova Migração
echo ================================
docker-compose exec web flask db migrate -m "%2"
goto end

:reset_db
echo ================================
echo Resetando Banco de Dados
echo ================================
echo [WARNING] ATENÇÃO: Isso irá apagar todos os dados do banco!
set /p confirm="Tem certeza? (y/N): "
if /i "!confirm!"=="y" (
    docker-compose down
    docker volume rm gestao_terapia_postgres_data 2>nul
    docker-compose up -d db
    timeout /t 10 /nobreak >nul
    docker-compose up -d web
    echo [INFO] Banco de dados resetado com sucesso!
) else (
    echo [INFO] Operação cancelada.
)
goto end

:help
echo Uso: %0 [COMANDO]
echo.
echo Comandos disponíveis:
echo   start              Iniciar todos os serviços
echo   stop               Parar todos os serviços
echo   restart            Reiniciar todos os serviços
echo   logs               Visualizar logs em tempo real
echo   shell              Acessar shell do container da aplicação
echo   exec [comando]     Executar comando no container da aplicação
echo   migrate            Executar migrações do banco
echo   create-migration   Criar nova migração
echo   reset-db           Resetar banco de dados (CUIDADO!)
echo   help               Mostrar esta ajuda
echo.
echo Exemplos:
echo   %0 start
echo   %0 logs
echo   %0 exec python -c "print('Hello World')"
echo   %0 create-migration "Adicionar tabela de comentários"

:end
endlocal
