"""
Modelos SQLAlchemy para o sistema de gestão terapêutica
"""

from datetime import datetime, date, timedelta
from flask_login import UserMixin
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
import hashlib
import uuid


class User(UserMixin, db.Model):
    """Modelo de usuário do sistema"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='patient')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    professional = db.relationship('Professional', backref='user', uselist=False, cascade='all, delete-orphan')
    patient = db.relationship('Patient', backref='user', uselist=False, cascade='all, delete-orphan')
    
    def set_password(self, password):
        """Define a senha do usuário"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Verifica a senha do usuário"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """Verifica se o usuário é admin"""
        return self.role == 'admin'
    
    def is_professional(self):
        """Verifica se o usuário é profissional"""
        return self.role == 'professional'
    
    def is_patient(self):
        """Verifica se o usuário é paciente"""
        return self.role == 'patient'
    
    def get_professional(self):
        """Retorna o perfil profissional se existir"""
        if self.is_professional():
            return self.professional
        return None
    
    def get_patient(self):
        """Retorna o perfil paciente se existir"""
        if self.is_patient():
            return self.patient
        return None
    
    def __repr__(self):
        return f'<User {self.username}>'


class Professional(db.Model):
    """Modelo de profissional"""
    __tablename__ = 'professionals'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    name = db.Column(db.String(100), nullable=False)
    specialty = db.Column(db.String(100), nullable=False)
    license_number = db.Column(db.String(50), nullable=False, unique=True)
    phone = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    patients = db.relationship('Patient', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    schedules = db.relationship('Schedule', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    appointments = db.relationship('Appointment', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    
    def get_patients(self):
        """Retorna todos os pacientes do profissional"""
        return self.patients.all()
    
    def __repr__(self):
        return f'<Professional {self.name}>'


class Patient(db.Model):
    """Modelo de paciente"""
    __tablename__ = 'patients'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, unique=True)
    professional_id = db.Column(db.Integer, db.ForeignKey('professionals.id'), nullable=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    date_of_birth = db.Column(db.Date)
    address = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    appointments = db.relationship('Appointment', backref='patient', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Patient {self.name}>'


class Schedule(db.Model):
    """Modelo de horários de agenda"""
    __tablename__ = 'schedules'
    
    id = db.Column(db.Integer, primary_key=True)
    professional_id = db.Column(db.Integer, db.ForeignKey('professionals.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0 = Monday, 1 = Tuesday, etc.
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    
    # Constraint para evitar horários duplicados
    __table_args__ = (db.UniqueConstraint('professional_id', 'day_of_week', 'start_time', 'end_time'),)
    
    def get_day_name(self):
        """Retorna o nome do dia da semana"""
        days = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo']
        return days[self.day_of_week]
    
    @staticmethod
    def get_available_slots(professional_id, date_str):
        """Retorna os horários disponíveis para uma data específica"""
        from datetime import datetime, time
        
        # Converter string para objeto date
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        day_of_week = target_date.weekday()
        
        # Buscar horários configurados para este dia da semana
        schedules = Schedule.query.filter_by(
            professional_id=professional_id,
            day_of_week=day_of_week
        ).all()
        
        if not schedules:
            return []
        
        # Buscar agendamentos existentes para esta data
        booked_appointments = Appointment.query.filter_by(
            professional_id=professional_id,
            appointment_date=target_date
        ).filter(Appointment.status != 'cancelled').all()
        
        # Criar conjunto de horários ocupados
        booked_times = set()
        for appointment in booked_appointments:
            booked_times.add((appointment.start_time, appointment.end_time))
        
        # Gerar slots disponíveis
        available_slots = []
        now = datetime.now()
        current_date = now.date()
        current_time = now.time()
        
        for schedule in schedules:
            # Gerar slots de 1 hora dentro do horário de trabalho
            current_slot_time = schedule.start_time
            
            while current_slot_time < schedule.end_time:
                # Calcular horário de fim do slot (1 hora depois)
                end_slot_time = (datetime.combine(date.today(), current_slot_time) + timedelta(hours=1)).time()
                
                # Verificar se o slot não ultrapassa o horário de trabalho
                if end_slot_time <= schedule.end_time:
                    # Verificar se não está no passado
                    if target_date > current_date or (target_date == current_date and current_slot_time > current_time):
                        # Verificar se não está ocupado
                        if (current_slot_time, end_slot_time) not in booked_times:
                            available_slots.append({
                                'start_time': current_slot_time,
                                'end_time': end_slot_time,
                                'formatted': f"{current_slot_time.strftime('%H:%M')} - {end_slot_time.strftime('%H:%M')}"
                            })
                
                # Próximo slot (incrementar 1 hora)
                current_slot_time = end_slot_time
        
        return available_slots
    
    def __repr__(self):
        return f'<Schedule {self.get_day_name()} {self.start_time}-{self.end_time}>'


class Appointment(db.Model):
    """Modelo de agendamentos"""
    __tablename__ = 'appointments'
    
    id = db.Column(db.Integer, primary_key=True)
    professional_id = db.Column(db.Integer, db.ForeignKey('professionals.id'), nullable=False)
    patient_id = db.Column(db.Integer, db.ForeignKey('patients.id'), nullable=False)
    appointment_date = db.Column(db.Date, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='scheduled')  # 'scheduled', 'completed', 'cancelled'
    notes = db.Column(db.Text)
    meeting_link = db.Column(db.String(255))
    post_appointment_notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @staticmethod
    def generate_meet_link(professional_id, patient_id, appointment_date, start_time):
        """Gera um link do Google Meet para a consulta"""
        try:
            from app.google_calendar import create_google_meet_event
            from datetime import datetime, timedelta
            from flask import session, current_app
            
            # Obter informações do profissional e paciente
            professional = Professional.query.get(professional_id)
            patient = Patient.query.get(patient_id)
            
            if not professional or not patient:
                raise ValueError("Profissional ou paciente não encontrado")
            
            # Converter para datetime
            if isinstance(appointment_date, str):
                appointment_datetime = datetime.strptime(f"{appointment_date} {start_time}", "%Y-%m-%d %H:%M:%S")
            else:
                appointment_datetime = datetime.combine(appointment_date, start_time)
            
            # Definir horário de término (1 hora após o início)
            end_datetime = appointment_datetime + timedelta(hours=1)
            
            # Criar evento no Google Calendar
            summary = f"Consulta: {professional.name} - {patient.name}"
            description = f"Consulta agendada entre {professional.name} e {patient.name}"
            
            attendees = []
            if patient.email:
                attendees.append(patient.email)
            
            # Tentar criar evento real
            result = create_google_meet_event(
                summary=summary,
                description=description,
                start_time=appointment_datetime,
                end_time=end_datetime,
                attendees=attendees
            )
            
            if result and result != "pending_google_auth":
                return result
            
        except Exception as e:
            print(f"Erro ao criar evento no Google Calendar: {e}")
        
        # Fallback: gerar link fictício
        meeting_id = hashlib.md5(f"{professional_id}-{patient_id}-{appointment_date}-{start_time}-{uuid.uuid4()}".encode()).hexdigest()[:10]
        date_str = appointment_date.strftime('%Y%m%d') if hasattr(appointment_date, 'strftime') else str(appointment_date).replace('-', '')
        return f"https://meet.google.com/{meeting_id}-{date_str}"
    
    def get_patient(self):
        """Retorna o paciente do agendamento"""
        return self.patient
    
    def get_professional(self):
        """Retorna o profissional do agendamento"""
        return self.professional
    
    def __repr__(self):
        return f'<Appointment {self.appointment_date} {self.start_time}>'
