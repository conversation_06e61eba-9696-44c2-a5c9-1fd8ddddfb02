from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date, time, timedelta
import sqlite3
import os
import uuid
import hashlib

# Database setup
DATABASE_PATH = 'app.db'

def get_db():
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    # Remover o banco de dados existente para garantir que as alterações sejam aplicadas
    if os.path.exists(DATABASE_PATH):
        os.remove(DATABASE_PATH)

    conn = get_db()
    cursor = conn.cursor()

    # Create tables
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS professional (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER UNIQUE NOT NULL,
        name TEXT NOT NULL,
        specialty TEXT NOT NULL,
        license_number TEXT NOT NULL,
        phone TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS patient (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        professional_id INTEGER,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT NOT NULL,
        date_of_birth DATE NOT NULL,
        address TEXT NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        user_id INTEGER UNIQUE,
        FOREIGN KEY (professional_id) REFERENCES professional (id),
        FOREIGN KEY (user_id) REFERENCES user (id)
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS schedule (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        professional_id INTEGER NOT NULL,
        day_of_week INTEGER NOT NULL, -- 0 = Monday, 1 = Tuesday, etc.
        start_time TEXT NOT NULL,     -- Format: HH:MM
        end_time TEXT NOT NULL,       -- Format: HH:MM
        FOREIGN KEY (professional_id) REFERENCES professional (id),
        UNIQUE(professional_id, day_of_week, start_time, end_time)
    )
    ''')

    cursor.execute('''
    CREATE TABLE IF NOT EXISTS appointment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        professional_id INTEGER NOT NULL,
        patient_id INTEGER NOT NULL,
        appointment_date DATE NOT NULL,
        start_time TEXT NOT NULL,     -- Format: HH:MM
        end_time TEXT NOT NULL,       -- Format: HH:MM
        status TEXT NOT NULL,         -- 'scheduled', 'completed', 'cancelled'
        notes TEXT,
        meeting_link TEXT,            -- Google Meet link
        post_appointment_notes TEXT,  -- Notes after appointment (professional's observations)
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (professional_id) REFERENCES professional (id),
        FOREIGN KEY (patient_id) REFERENCES patient (id)
    )
    ''')

    conn.commit()
    conn.close()

class User(UserMixin):
    def __init__(self, id=None, username=None, email=None, password_hash=None, role=None, created_at=None):
        self.id = id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.role = role
        self.created_at = created_at

    @staticmethod
    def get_by_id(user_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user WHERE id = ?', (user_id,))
        user_data = cursor.fetchone()
        conn.close()

        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                email=user_data['email'],
                password_hash=user_data['password_hash'],
                role=user_data['role'],
                created_at=user_data['created_at']
            )
        return None

    @staticmethod
    def get_by_username(username):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user WHERE username = ?', (username,))
        user_data = cursor.fetchone()
        conn.close()

        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                email=user_data['email'],
                password_hash=user_data['password_hash'],
                role=user_data['role'],
                created_at=user_data['created_at']
            )
        return None

    @staticmethod
    def get_by_email(email):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM user WHERE email = ?', (email,))
        user_data = cursor.fetchone()
        conn.close()

        if user_data:
            return User(
                id=user_data['id'],
                username=user_data['username'],
                email=user_data['email'],
                password_hash=user_data['password_hash'],
                role=user_data['role'],
                created_at=user_data['created_at']
            )
        return None

    def save(self):
        conn = get_db()
        cursor = conn.cursor()

        if self.id is None:
            cursor.execute(
                'INSERT INTO user (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
                (self.username, self.email, self.password_hash, self.role)
            )
            self.id = cursor.lastrowid
        else:
            cursor.execute(
                'UPDATE user SET username = ?, email = ?, password_hash = ?, role = ? WHERE id = ?',
                (self.username, self.email, self.password_hash, self.role, self.id)
            )

        conn.commit()
        conn.close()
        return self

    def delete(self):
        if self.id is None:
            return

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM user WHERE id = ?', (self.id,))
        conn.commit()
        conn.close()

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

    def is_professional(self):
        return self.role == 'professional'

    def is_patient(self):
        return self.role == 'patient'

    def get_professional(self):
        if not self.is_professional():
            return None

        return Professional.get_by_user_id(self.id)

    def get_patient(self):
        if not self.is_patient():
            return None

        return Patient.get_by_user_id(self.id)

    def __repr__(self):
        return f'<User {self.username}>'


class Professional:
    def __init__(self, id=None, user_id=None, name=None, specialty=None, license_number=None, phone=None, created_at=None):
        self.id = id
        self.user_id = user_id
        self.name = name
        self.specialty = specialty
        self.license_number = license_number
        self.phone = phone
        self.created_at = created_at

    @staticmethod
    def get_by_id(professional_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM professional WHERE id = ?', (professional_id,))
        prof_data = cursor.fetchone()
        conn.close()

        if prof_data:
            return Professional(
                id=prof_data['id'],
                user_id=prof_data['user_id'],
                name=prof_data['name'],
                specialty=prof_data['specialty'],
                license_number=prof_data['license_number'],
                phone=prof_data['phone'],
                created_at=prof_data['created_at']
            )
        return None

    @staticmethod
    def get_by_user_id(user_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM professional WHERE user_id = ?', (user_id,))
        prof_data = cursor.fetchone()
        conn.close()

        if prof_data:
            return Professional(
                id=prof_data['id'],
                user_id=prof_data['user_id'],
                name=prof_data['name'],
                specialty=prof_data['specialty'],
                license_number=prof_data['license_number'],
                phone=prof_data['phone'],
                created_at=prof_data['created_at']
            )
        return None

    @staticmethod
    def get_all():
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM professional')
        prof_data_list = cursor.fetchall()
        conn.close()

        return [
            Professional(
                id=prof_data['id'],
                user_id=prof_data['user_id'],
                name=prof_data['name'],
                specialty=prof_data['specialty'],
                license_number=prof_data['license_number'],
                phone=prof_data['phone'],
                created_at=prof_data['created_at']
            )
            for prof_data in prof_data_list
        ]

    def save(self):
        conn = get_db()
        cursor = conn.cursor()

        if self.id is None:
            cursor.execute(
                'INSERT INTO professional (user_id, name, specialty, license_number, phone) VALUES (?, ?, ?, ?, ?)',
                (self.user_id, self.name, self.specialty, self.license_number, self.phone)
            )
            self.id = cursor.lastrowid
        else:
            cursor.execute(
                'UPDATE professional SET user_id = ?, name = ?, specialty = ?, license_number = ?, phone = ? WHERE id = ?',
                (self.user_id, self.name, self.specialty, self.license_number, self.phone, self.id)
            )

        conn.commit()
        conn.close()
        return self

    def delete(self):
        if self.id is None:
            return

        conn = get_db()
        cursor = conn.cursor()

        # Delete all patients associated with this professional
        cursor.execute('DELETE FROM patient WHERE professional_id = ?', (self.id,))

        # Delete the professional
        cursor.execute('DELETE FROM professional WHERE id = ?', (self.id,))

        conn.commit()
        conn.close()

    def get_patients(self):
        return Patient.get_by_professional_id(self.id)

    def __repr__(self):
        return f'<Professional {self.name}>'


class Patient:
    def __init__(self, id=None, professional_id=None, name=None, email=None, phone=None,
                 date_of_birth=None, address=None, notes=None, created_at=None, user_id=None):
        self.id = id
        self.professional_id = professional_id
        self.name = name
        self.email = email
        self.phone = phone
        self.date_of_birth = date_of_birth
        self.address = address
        self.notes = notes
        self.created_at = created_at
        self.user_id = user_id

    @staticmethod
    def get_by_id(patient_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patient WHERE id = ?', (patient_id,))
        patient_data = cursor.fetchone()
        conn.close()

        if patient_data:
            return Patient(
                id=patient_data['id'],
                professional_id=patient_data['professional_id'],
                name=patient_data['name'],
                email=patient_data['email'],
                phone=patient_data['phone'],
                date_of_birth=patient_data['date_of_birth'],
                address=patient_data['address'],
                notes=patient_data['notes'],
                created_at=patient_data['created_at']
            )
        return None

    @staticmethod
    def get_by_professional_id(professional_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patient WHERE professional_id = ?', (professional_id,))
        patient_data_list = cursor.fetchall()
        conn.close()

        return [
            Patient(
                id=patient_data['id'],
                professional_id=patient_data['professional_id'],
                name=patient_data['name'],
                email=patient_data['email'],
                phone=patient_data['phone'],
                date_of_birth=patient_data['date_of_birth'],
                address=patient_data['address'],
                notes=patient_data['notes'],
                created_at=patient_data['created_at'],
                user_id=patient_data['user_id']
            )
            for patient_data in patient_data_list
        ]

    @staticmethod
    def get_by_user_id(user_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM patient WHERE user_id = ?', (user_id,))
        patient_data = cursor.fetchone()
        conn.close()

        if patient_data:
            return Patient(
                id=patient_data['id'],
                professional_id=patient_data['professional_id'],
                name=patient_data['name'],
                email=patient_data['email'],
                phone=patient_data['phone'],
                date_of_birth=patient_data['date_of_birth'],
                address=patient_data['address'],
                notes=patient_data['notes'],
                created_at=patient_data['created_at'],
                user_id=patient_data['user_id']
            )
        return None

    def save(self):
        conn = get_db()
        cursor = conn.cursor()

        if self.id is None:
            cursor.execute(
                'INSERT INTO patient (professional_id, name, email, phone, date_of_birth, address, notes, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                (self.professional_id, self.name, self.email, self.phone, self.date_of_birth, self.address, self.notes, self.user_id)
            )
            self.id = cursor.lastrowid
        else:
            cursor.execute(
                'UPDATE patient SET professional_id = ?, name = ?, email = ?, phone = ?, date_of_birth = ?, address = ?, notes = ?, user_id = ? WHERE id = ?',
                (self.professional_id, self.name, self.email, self.phone, self.date_of_birth, self.address, self.notes, self.user_id, self.id)
            )

        conn.commit()
        conn.close()
        return self

    def delete(self):
        if self.id is None:
            return

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM patient WHERE id = ?', (self.id,))
        conn.commit()
        conn.close()

    def __repr__(self):
        return f'<Patient {self.name}>'


class Schedule:
    def __init__(self, id=None, professional_id=None, day_of_week=None, start_time=None, end_time=None):
        self.id = id
        self.professional_id = professional_id
        self.day_of_week = day_of_week
        self.start_time = start_time
        self.end_time = end_time

    @staticmethod
    def get_by_id(schedule_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM schedule WHERE id = ?', (schedule_id,))
        schedule_data = cursor.fetchone()
        conn.close()

        if schedule_data:
            return Schedule(
                id=schedule_data['id'],
                professional_id=schedule_data['professional_id'],
                day_of_week=schedule_data['day_of_week'],
                start_time=schedule_data['start_time'],
                end_time=schedule_data['end_time']
            )
        return None

    @staticmethod
    def get_by_professional_id(professional_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM schedule WHERE professional_id = ? ORDER BY day_of_week, start_time', (professional_id,))
        schedule_data_list = cursor.fetchall()
        conn.close()

        return [
            Schedule(
                id=schedule_data['id'],
                professional_id=schedule_data['professional_id'],
                day_of_week=schedule_data['day_of_week'],
                start_time=schedule_data['start_time'],
                end_time=schedule_data['end_time']
            )
            for schedule_data in schedule_data_list
        ]

    def save(self):
        conn = get_db()
        cursor = conn.cursor()

        if self.id is None:
            cursor.execute(
                'INSERT INTO schedule (professional_id, day_of_week, start_time, end_time) VALUES (?, ?, ?, ?)',
                (self.professional_id, self.day_of_week, self.start_time, self.end_time)
            )
            self.id = cursor.lastrowid
        else:
            cursor.execute(
                'UPDATE schedule SET professional_id = ?, day_of_week = ?, start_time = ?, end_time = ? WHERE id = ?',
                (self.professional_id, self.day_of_week, self.start_time, self.end_time, self.id)
            )

        conn.commit()
        conn.close()
        return self

    def delete(self):
        if self.id is None:
            return

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM schedule WHERE id = ?', (self.id,))
        conn.commit()
        conn.close()

    def get_day_name(self):
        days = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo']
        return days[self.day_of_week]

    def __repr__(self):
        return f'<Schedule {self.get_day_name()} {self.start_time}-{self.end_time}>'


class Appointment:
    def __init__(self, id=None, professional_id=None, patient_id=None, appointment_date=None,
                 start_time=None, end_time=None, status=None, notes=None, meeting_link=None,
                 post_appointment_notes=None, created_at=None):
        self.id = id
        self.professional_id = professional_id
        self.patient_id = patient_id
        self.appointment_date = appointment_date
        self.start_time = start_time
        self.end_time = end_time
        self.status = status
        self.notes = notes
        self.meeting_link = meeting_link
        self.post_appointment_notes = post_appointment_notes
        self.created_at = created_at

    @staticmethod
    def generate_meet_link(professional_id, patient_id, appointment_date, start_time):
        """
        Gera um link do Google Meet para a consulta usando a API do Google Calendar.
        Se a integração falhar, gera um link fictício como fallback.

        Retorna:
            - Um link do Google Meet se a criação for bem-sucedida
            - Um link fictício como fallback em caso de erro
        """
        try:
            from app.google_calendar import create_google_meet_event
            from datetime import datetime, timedelta
            from flask import session, current_app

            # Verificar se estamos em um contexto de requisição
            if not current_app.config.get('TESTING', False):
                try:
                    from flask import request
                    # Se não estamos em um contexto de requisição, usar o fallback
                    if not request:
                        raise RuntimeError("Não estamos em um contexto de requisição")
                except RuntimeError:
                    # Fallback para quando não estamos em um contexto de requisição
                    meeting_id = hashlib.md5(f"{professional_id}-{patient_id}-{appointment_date}-{start_time}-{uuid.uuid4()}".encode()).hexdigest()[:10]
                    return f"https://meet.google.com/{meeting_id}-{appointment_date.replace('-', '')}"

            # Obter informações do profissional e paciente
            professional = Professional.get_by_id(professional_id)
            patient = Patient.get_by_id(patient_id)

            if not professional or not patient:
                raise ValueError("Profissional ou paciente não encontrado")

            # Converter a data e hora para objetos datetime
            appointment_datetime = datetime.strptime(f"{appointment_date} {start_time}", "%Y-%m-%d %H:%M")

            # Definir o horário de término (1 hora após o início)
            end_datetime = appointment_datetime + timedelta(hours=1)

            # Criar o evento no Google Calendar
            summary = f"Consulta: {professional.name} - {patient.name}"
            description = f"Consulta agendada entre {professional.name} e {patient.name}"

            # Lista de e-mails dos participantes
            attendees = []
            if patient.email:
                attendees.append(patient.email)

            # Armazenar informações do agendamento na sessão para uso após a autenticação
            try:
                session['appointment_info'] = {
                    'professional_id': professional_id,
                    'patient_id': patient_id,
                    'appointment_date': appointment_date,
                    'start_time': start_time,
                    'summary': summary,
                    'description': description,
                    'attendees': attendees
                }

                # Criar o evento e obter o link do Google Meet
                result = create_google_meet_event(
                    summary=summary,
                    description=description,
                    start_time=appointment_datetime,
                    end_time=end_datetime,
                    attendees=attendees
                )

                # Se o resultado for um redirecionamento, marcar na sessão que precisamos autenticar
                if hasattr(result, 'status_code') and result.status_code == 302:
                    session['needs_google_auth'] = True
                    session['auth_redirect_url'] = result.location
                    # Retornar um link temporário que será atualizado após a autenticação
                    return "pending_google_auth"

                if result:
                    return result

                # Se falhar, usar o método de fallback
                print("Falha ao criar evento no Google Calendar. Usando link fictício como fallback.")
            except Exception as e:
                print(f"Erro ao acessar a sessão: {e}")
        except Exception as e:
            print(f"Erro ao gerar link do Google Meet: {e}")

        # Fallback: Criar um link fictício do Google Meet
        meeting_id = hashlib.md5(f"{professional_id}-{patient_id}-{appointment_date}-{start_time}-{uuid.uuid4()}".encode()).hexdigest()[:10]
        return f"https://meet.google.com/{meeting_id}-{appointment_date.replace('-', '')}"

    @staticmethod
    def get_by_id(appointment_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM appointment WHERE id = ?', (appointment_id,))
        appointment_data = cursor.fetchone()
        conn.close()

        if appointment_data:
            return Appointment(
                id=appointment_data['id'],
                professional_id=appointment_data['professional_id'],
                patient_id=appointment_data['patient_id'],
                appointment_date=appointment_data['appointment_date'],
                start_time=appointment_data['start_time'],
                end_time=appointment_data['end_time'],
                status=appointment_data['status'],
                notes=appointment_data['notes'],
                meeting_link=appointment_data['meeting_link'],
                post_appointment_notes=appointment_data['post_appointment_notes'],
                created_at=appointment_data['created_at']
            )
        return None

    @staticmethod
    def get_by_professional_id(professional_id, start_date=None, end_date=None):
        conn = get_db()
        cursor = conn.cursor()

        query = 'SELECT * FROM appointment WHERE professional_id = ?'
        params = [professional_id]

        if start_date:
            query += ' AND appointment_date >= ?'
            params.append(start_date)

        if end_date:
            query += ' AND appointment_date <= ?'
            params.append(end_date)

        query += ' ORDER BY appointment_date, start_time'

        cursor.execute(query, params)
        appointment_data_list = cursor.fetchall()
        conn.close()

        return [
            Appointment(
                id=appointment_data['id'],
                professional_id=appointment_data['professional_id'],
                patient_id=appointment_data['patient_id'],
                appointment_date=appointment_data['appointment_date'],
                start_time=appointment_data['start_time'],
                end_time=appointment_data['end_time'],
                status=appointment_data['status'],
                notes=appointment_data['notes'],
                meeting_link=appointment_data['meeting_link'],
                post_appointment_notes=appointment_data['post_appointment_notes'],
                created_at=appointment_data['created_at']
            )
            for appointment_data in appointment_data_list
        ]

    @staticmethod
    def get_by_patient_id(patient_id):
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM appointment WHERE patient_id = ? ORDER BY appointment_date, start_time', (patient_id,))
        appointment_data_list = cursor.fetchall()
        conn.close()

        return [
            Appointment(
                id=appointment_data['id'],
                professional_id=appointment_data['professional_id'],
                patient_id=appointment_data['patient_id'],
                appointment_date=appointment_data['appointment_date'],
                start_time=appointment_data['start_time'],
                end_time=appointment_data['end_time'],
                status=appointment_data['status'],
                notes=appointment_data['notes'],
                meeting_link=appointment_data['meeting_link'],
                post_appointment_notes=appointment_data['post_appointment_notes'],
                created_at=appointment_data['created_at']
            )
            for appointment_data in appointment_data_list
        ]

    @staticmethod
    def get_available_slots(professional_id, date_str):
        """
        Retorna os horários disponíveis para um profissional em uma data específica
        """
        # Converter a string de data para um objeto date
        appointment_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # Obter o dia da semana (0 = Segunda, 1 = Terça, etc.)
        day_of_week = appointment_date.weekday()

        # Obter os horários de agenda do profissional para este dia da semana
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute(
            'SELECT * FROM schedule WHERE professional_id = ? AND day_of_week = ? ORDER BY start_time',
            (professional_id, day_of_week)
        )
        schedule_data_list = cursor.fetchall()

        # Se não houver horários definidos para este dia, retornar lista vazia
        if not schedule_data_list:
            conn.close()
            return []

        # Obter os agendamentos existentes para esta data
        cursor.execute(
            'SELECT start_time, end_time FROM appointment WHERE professional_id = ? AND appointment_date = ? AND status != "cancelled"',
            (professional_id, date_str)
        )
        booked_slots = cursor.fetchall()
        conn.close()

        # Converter os horários reservados para um conjunto para facilitar a verificação
        booked_times = set()
        for slot in booked_slots:
            booked_times.add((slot['start_time'], slot['end_time']))

        # Obter a data e hora atual
        now = datetime.now()
        current_date = now.date()
        current_time_str = now.strftime('%H:%M')

        # Gerar slots disponíveis com base na agenda e nos horários já reservados
        available_slots = []
        for schedule in schedule_data_list:
            start_time = schedule['start_time']
            end_time = schedule['end_time']

            # Dividir o período em slots de 1 hora
            current_time = start_time
            while current_time < end_time:
                # Calcular o horário de término do slot (1 hora depois)
                hour, minute = map(int, current_time.split(':'))
                next_hour = hour + 1 if hour < 23 else 0
                next_time = f"{next_hour:02d}:{minute:02d}"

                # Verificar se este slot não está reservado e não é um horário passado
                is_past = False
                if appointment_date == current_date and current_time < current_time_str:
                    is_past = True

                if (current_time, next_time) not in booked_times and not is_past:
                    available_slots.append({
                        'start_time': current_time,
                        'end_time': next_time,
                        'formatted': f"{current_time} - {next_time}"
                    })

                # Avançar para o próximo slot
                current_time = next_time

        return available_slots

    def save(self):
        conn = get_db()
        cursor = conn.cursor()

        if self.id is None:
            cursor.execute(
                'INSERT INTO appointment (professional_id, patient_id, appointment_date, start_time, end_time, status, notes, meeting_link, post_appointment_notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
                (self.professional_id, self.patient_id, self.appointment_date, self.start_time, self.end_time, self.status, self.notes, self.meeting_link, self.post_appointment_notes)
            )
            self.id = cursor.lastrowid
        else:
            cursor.execute(
                'UPDATE appointment SET professional_id = ?, patient_id = ?, appointment_date = ?, start_time = ?, end_time = ?, status = ?, notes = ?, meeting_link = ?, post_appointment_notes = ? WHERE id = ?',
                (self.professional_id, self.patient_id, self.appointment_date, self.start_time, self.end_time, self.status, self.notes, self.meeting_link, self.post_appointment_notes, self.id)
            )

        conn.commit()
        conn.close()
        return self

    def delete(self):
        if self.id is None:
            return

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM appointment WHERE id = ?', (self.id,))
        conn.commit()
        conn.close()

    def get_patient(self):
        return Patient.get_by_id(self.patient_id)

    def get_professional(self):
        return Professional.get_by_id(self.professional_id)

    def __repr__(self):
        return f'<Appointment {self.appointment_date} {self.start_time}-{self.end_time}>'
