{% extends "base.html" %}

{% block title %}Admin Dashboard - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Dashboard do Administrador</h2>
    <a href="{{ url_for('main.add_professional') }}" class="btn btn-primary">Adicionar Profissional</a>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Profissionais Cadastrados</h5>
    </div>
    <div class="card-body">
        {% if professionals %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Especialidade</th>
                            <th>Número de Registro</th>
                            <th>Telefone</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for professional in professionals %}
                            <tr>
                                <td>{{ professional.name }}</td>
                                <td>{{ professional.specialty }}</td>
                                <td>{{ professional.license_number }}</td>
                                <td>{{ professional.phone }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('main.edit_professional', id=professional.id) }}" class="btn btn-outline-primary">Editar</a>
                                        <a href="{{ url_for('main.delete_professional', id=professional.id) }}" class="btn btn-outline-danger" onclick="return confirm('Tem certeza que deseja excluir este profissional?')">Excluir</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                Nenhum profissional cadastrado. <a href="{{ url_for('main.add_professional') }}">Adicionar um profissional</a>.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
