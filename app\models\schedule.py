"""
Schedule Model - Single Responsibility Principle
Entidade de domínio responsável pela representação da agenda
"""

from datetime import datetime, date, time, timedelta
from app import db
from .base_model import BaseModel


class Schedule(BaseModel):
    """
    Entidade Schedule - representa horários de trabalho do profissional
    Responsabilidades:
    - Definir horários de trabalho
    - Validar disponibilidade
    - Gerar slots de tempo
    """
    __tablename__ = 'schedules'
    
    professional_id = db.Column(db.Integer, db.ForeignKey('professionals.id'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0 = Monday, 6 = Sunday
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    slot_duration = db.Column(db.Integer, default=60, nullable=False)  # Duration in minutes
    
    # Constraint para evitar horários duplicados
    __table_args__ = (
        db.UniqueConstraint('professional_id', 'day_of_week', 'start_time', 'end_time'),
        db.CheckConstraint('day_of_week >= 0 AND day_of_week <= 6', name='valid_day_of_week'),
        db.CheckConstraint('slot_duration > 0', name='positive_slot_duration')
    )
    
    # Mapeamento de dias da semana
    DAYS_OF_WEEK = {
        0: 'Segunda-feira',
        1: 'Terça-feira', 
        2: 'Quarta-feira',
        3: 'Quinta-feira',
        4: 'Sexta-feira',
        5: 'Sábado',
        6: 'Domingo'
    }
    
    def __init__(self, **kwargs):
        """Inicializa o horário com validações"""
        super().__init__(**kwargs)
        self._validate_schedule()
    
    def _validate_schedule(self):
        """Valida os dados do horário"""
        if not (0 <= self.day_of_week <= 6):
            raise ValueError("Day of week must be between 0 (Monday) and 6 (Sunday)")
        
        if self.start_time >= self.end_time:
            raise ValueError("Start time must be before end time")
        
        if self.slot_duration <= 0:
            raise ValueError("Slot duration must be positive")
        
        # Validar duração mínima do período
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        duration_minutes = (end_datetime - start_datetime).total_seconds() / 60
        
        if duration_minutes < self.slot_duration:
            raise ValueError("Schedule duration must be at least one slot duration")
    
    def get_day_name(self) -> str:
        """Retorna o nome do dia da semana"""
        return self.DAYS_OF_WEEK.get(self.day_of_week, 'Unknown')
    
    def get_duration_hours(self) -> float:
        """Retorna a duração do horário em horas"""
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        duration = end_datetime - start_datetime
        return duration.total_seconds() / 3600
    
    def get_total_slots(self) -> int:
        """Retorna o número total de slots disponíveis"""
        duration_minutes = self.get_duration_hours() * 60
        return int(duration_minutes // self.slot_duration)
    
    def generate_time_slots(self) -> list:
        """Gera lista de slots de tempo disponíveis"""
        slots = []
        current_time = self.start_time
        
        while True:
            # Calcular horário de fim do slot
            start_datetime = datetime.combine(date.today(), current_time)
            end_datetime = start_datetime + timedelta(minutes=self.slot_duration)
            end_time = end_datetime.time()
            
            # Verificar se o slot cabe no horário de trabalho
            if end_time <= self.end_time:
                slots.append({
                    'start_time': current_time,
                    'end_time': end_time,
                    'formatted': f"{current_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')}"
                })
                
                # Próximo slot
                current_time = end_time
            else:
                break
        
        return slots
    
    def is_time_within_schedule(self, check_time: time) -> bool:
        """Verifica se um horário está dentro do período de trabalho"""
        return self.start_time <= check_time <= self.end_time
    
    def overlaps_with(self, other_schedule) -> bool:
        """Verifica se há sobreposição com outro horário"""
        if self.day_of_week != other_schedule.day_of_week:
            return False
        
        return not (self.end_time <= other_schedule.start_time or 
                   self.start_time >= other_schedule.end_time)
    
    def activate(self):
        """Ativa o horário"""
        self.is_active = True
        self.save()
    
    def deactivate(self):
        """Desativa o horário"""
        self.is_active = False
        self.save()
    
    def update_times(self, start_time: time, end_time: time):
        """Atualiza os horários"""
        self.start_time = start_time
        self.end_time = end_time
        self._validate_schedule()
        self.save()
    
    def update_slot_duration(self, duration_minutes: int):
        """Atualiza a duração dos slots"""
        if duration_minutes <= 0:
            raise ValueError("Slot duration must be positive")
        
        self.slot_duration = duration_minutes
        self.save()
    
    @classmethod
    def get_available_slots_for_date(cls, professional_id: int, target_date: date) -> list:
        """
        Retorna slots disponíveis para uma data específica
        Considera agendamentos existentes
        """
        from .appointment import Appointment
        
        day_of_week = target_date.weekday()
        
        # Buscar horários configurados para este dia
        schedules = cls.query.filter_by(
            professional_id=professional_id,
            day_of_week=day_of_week,
            is_active=True
        ).all()
        
        if not schedules:
            return []
        
        # Buscar agendamentos existentes para esta data
        existing_appointments = Appointment.query.filter_by(
            professional_id=professional_id,
            appointment_date=target_date
        ).filter(Appointment.status != 'cancelled').all()
        
        # Criar conjunto de horários ocupados
        occupied_slots = set()
        for appointment in existing_appointments:
            occupied_slots.add((appointment.start_time, appointment.end_time))
        
        # Gerar slots disponíveis
        available_slots = []
        current_datetime = datetime.now()
        
        for schedule in schedules:
            slots = schedule.generate_time_slots()
            
            for slot in slots:
                # Verificar se não está no passado
                slot_datetime = datetime.combine(target_date, slot['start_time'])
                if slot_datetime <= current_datetime:
                    continue
                
                # Verificar se não está ocupado
                if (slot['start_time'], slot['end_time']) not in occupied_slots:
                    available_slots.append({
                        'schedule_id': schedule.id,
                        'start_time': slot['start_time'],
                        'end_time': slot['end_time'],
                        'formatted': slot['formatted'],
                        'day_name': schedule.get_day_name()
                    })
        
        # Ordenar por horário
        available_slots.sort(key=lambda x: x['start_time'])
        return available_slots
    
    def to_dict(self):
        """Converte para dicionário incluindo informações úteis"""
        data = super().to_dict()
        data['day_name'] = self.get_day_name()
        data['duration_hours'] = self.get_duration_hours()
        data['total_slots'] = self.get_total_slots()
        data['time_slots'] = self.generate_time_slots()
        return data
    
    def __repr__(self):
        return f'<Schedule {self.get_day_name()} {self.start_time}-{self.end_time}>'
