"""
Base Repository - Interface Segregation Principle
Classe base que define operações comuns de acesso a dados
"""

from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod
from app import db


class BaseRepository(ABC):
    """
    Repositório base que define operações CRUD comuns
    Implementa o padrão Repository para abstrair o acesso aos dados
    """
    
    def __init__(self, model_class):
        """Inicializa o repositório com a classe do modelo"""
        self.model_class = model_class
    
    def create(self, **kwargs) -> Any:
        """Cria uma nova entidade"""
        entity = self.model_class(**kwargs)
        return entity.save()
    
    def get_by_id(self, entity_id: int) -> Optional[Any]:
        """Busca entidade por ID"""
        return self.model_class.query.get(entity_id)
    
    def get_by_id_or_404(self, entity_id: int) -> Any:
        """Busca entidade por ID ou retorna 404"""
        return self.model_class.query.get_or_404(entity_id)
    
    def get_all(self) -> List[Any]:
        """Retorna todas as entidades"""
        return self.model_class.query.all()
    
    def get_all_active(self) -> List[Any]:
        """Retorna todas as entidades ativas (se aplicável)"""
        if hasattr(self.model_class, 'is_active'):
            return self.model_class.query.filter_by(is_active=True).all()
        return self.get_all()
    
    def find_by(self, **kwargs) -> List[Any]:
        """Busca entidades por critérios específicos"""
        return self.model_class.query.filter_by(**kwargs).all()
    
    def find_one_by(self, **kwargs) -> Optional[Any]:
        """Busca uma entidade por critérios específicos"""
        return self.model_class.query.filter_by(**kwargs).first()
    
    def update(self, entity: Any, **kwargs) -> Any:
        """Atualiza uma entidade"""
        return entity.update(**kwargs)
    
    def delete(self, entity: Any) -> None:
        """Remove uma entidade"""
        entity.delete()
    
    def delete_by_id(self, entity_id: int) -> bool:
        """Remove uma entidade por ID"""
        entity = self.get_by_id(entity_id)
        if entity:
            self.delete(entity)
            return True
        return False
    
    def count(self) -> int:
        """Conta o total de entidades"""
        return self.model_class.query.count()
    
    def count_by(self, **kwargs) -> int:
        """Conta entidades por critérios específicos"""
        return self.model_class.query.filter_by(**kwargs).count()
    
    def exists(self, entity_id: int) -> bool:
        """Verifica se uma entidade existe"""
        return self.get_by_id(entity_id) is not None
    
    def exists_by(self, **kwargs) -> bool:
        """Verifica se existe entidade com critérios específicos"""
        return self.find_one_by(**kwargs) is not None
    
    def paginate(self, page: int = 1, per_page: int = 20, **filters) -> Any:
        """Retorna entidades paginadas"""
        query = self.model_class.query
        
        if filters:
            query = query.filter_by(**filters)
        
        return query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
    
    def bulk_create(self, entities_data: List[Dict]) -> List[Any]:
        """Cria múltiplas entidades em lote"""
        entities = []
        for data in entities_data:
            entity = self.model_class(**data)
            entities.append(entity)
            db.session.add(entity)
        
        db.session.commit()
        return entities
    
    def bulk_update(self, updates: List[Dict]) -> None:
        """Atualiza múltiplas entidades em lote"""
        for update_data in updates:
            entity_id = update_data.pop('id')
            entity = self.get_by_id(entity_id)
            if entity:
                entity.update(**update_data)
    
    def bulk_delete(self, entity_ids: List[int]) -> int:
        """Remove múltiplas entidades em lote"""
        deleted_count = self.model_class.query.filter(
            self.model_class.id.in_(entity_ids)
        ).delete(synchronize_session=False)
        
        db.session.commit()
        return deleted_count
    
    @abstractmethod
    def get_repository_specific_methods(self):
        """
        Método abstrato para implementar métodos específicos do repositório
        Cada repositório filho deve implementar seus métodos específicos
        """
        pass
