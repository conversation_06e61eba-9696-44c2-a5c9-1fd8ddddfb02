"""
Domain Models - Clean Architecture
Entidades de domínio que representam os conceitos de negócio
"""

from .base_model import BaseModel
from .user import User
from .professional import Professional
from .patient import Patient
from .schedule import Schedule
from .appointment import Appointment

__all__ = [
    'BaseModel',
    'User',
    'Professional', 
    'Patient',
    'Schedule',
    'Appointment'
]
