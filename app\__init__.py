# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask import Flask
from flask_login import LoginManager
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import Config

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()

# Initialize login manager
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'

@login_manager.user_loader
def load_user(id):
    from app.models.user import User
    return User.query.get(int(id))

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)

    # Adicionar filtros personalizados
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ""

    # Register blueprints
    from app.controllers.auth_controller import auth_bp
    from app.controllers.professional_controller import professional_bp
    from app.controllers.patient_controller import patient_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(professional_bp)
    app.register_blueprint(patient_bp)

    # Create tables and seed data
    with app.app_context():
        db.create_all()
        seed_database()

    return app

def seed_database():
    """Seed the database with initial data using repositories"""
    from app.services.auth_service import AuthService
    from app.repositories.user_repository import UserRepository
    from app.repositories.professional_repository import ProfessionalRepository
    from app.repositories.patient_repository import PatientRepository
    from datetime import time

    try:
        auth_service = AuthService()
        user_repo = UserRepository()
        prof_repo = ProfessionalRepository()
        patient_repo = PatientRepository()

        # Create admin user
        if not user_repo.get_by_username('admin'):
            admin = auth_service.create_user(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                role='admin'
            )
            print("✅ Admin user created: username=admin, password=admin123")

        # Create professional user
        if not user_repo.get_by_username('profissional'):
            prof_user = auth_service.create_user(
                username='profissional',
                email='<EMAIL>',
                password='prof123',
                role='professional'
            )

            # Create professional profile
            professional = prof_repo.create(
                user_id=prof_user.id,
                name='Dr. João Silva',
                specialty='Psicologia Clínica',
                license_number='CRP-12345',
                phone='(11) 99999-9999'
            )
            print("✅ Professional created: Dr. João Silva")

        # Create patient user
        if not user_repo.get_by_username('paciente'):
            patient_user = auth_service.create_user(
                username='paciente',
                email='<EMAIL>',
                password='paciente123',
                role='patient'
            )

            # Create patient profile
            patient_profile = patient_repo.create(
                user_id=patient_user.id,
                name='Maria Silva',
                email='<EMAIL>',
                phone='(11) 98765-4321',
                address='Rua das Flores, 123'
            )
            print("✅ Patient created: Maria Silva")

        # Create Dr. Matheus Parreira
        if not user_repo.get_by_email('<EMAIL>'):
            matheus_user = auth_service.create_user(
                username='matheus_parreira',
                email='<EMAIL>',
                password='teste123',
                role='professional'
            )

            matheus_prof = prof_repo.create(
                user_id=matheus_user.id,
                name='Dr. Matheus Parreira',
                specialty='Psicologia Clínica',
                license_number='CRP-54321',
                phone='(11) 99999-9999'
            )
            print("✅ Dr. Matheus Parreira created")

        print("🎉 Database seeded successfully!")

    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.session.rollback()