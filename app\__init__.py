# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask import Flask
from flask_login import LoginManager
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import Config

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()

# Initialize login manager
login_manager = LoginManager()
login_manager.login_view = 'main.login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'

@login_manager.user_loader
def load_user(id):
    from app.models import User
    return User.query.get(int(id))

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)

    # Adicionar filtros personalizados
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ""

    # Register blueprints
    from app.routes import main
    app.register_blueprint(main)

    # Create tables and seed data
    with app.app_context():
        db.create_all()
        seed_database()

    return app

def seed_database():
    """Seed the database with initial data"""
    from app.models import User, Professional, Patient, Schedule, Appointment
    from datetime import datetime, timedelta, time
    import hashlib

    try:
        # Admin user
        admin = User.query.filter_by(username='admin').first()
        if admin is None:
            admin = User(username='admin', email='<EMAIL>', role='admin')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("Admin user created: username=admin, password=admin123")

        # Professional user
        prof = User.query.filter_by(username='profissional').first()
        if prof is None:
            prof = User(username='profissional', email='<EMAIL>', role='professional')
            prof.set_password('prof123')
            db.session.add(prof)
            db.session.commit()

            # Create professional profile
            professional = Professional(
                user_id=prof.id,
                name='Dr. João Silva',
                specialty='Psicologia Clínica',
                license_number='CRP-12345',
                phone='(11) 99999-9999'
            )
            db.session.add(professional)
            db.session.commit()
            print("Professional profile created for user 'profissional'")

        # Patient user
        patient = User.query.filter_by(username='paciente').first()
        if patient is None:
            patient = User(username='paciente', email='<EMAIL>', role='patient')
            patient.set_password('paciente123')
            db.session.add(patient)
            db.session.commit()

            # Create patient profile
            patient_profile = Patient(
                user_id=patient.id,
                name='Maria Silva',
                email='<EMAIL>',
                phone='(11) 98765-4321',
                address='Rua das Flores, 123'
            )
            db.session.add(patient_profile)
            db.session.commit()
            print("Patient profile created for user 'paciente'")

        # Dr. Matheus Parreira (from seed)
        matheus = User.query.filter_by(email='<EMAIL>').first()
        if matheus is None:
            matheus = User(username='matheus_parreira', email='<EMAIL>', role='professional')
            matheus.set_password('teste123')
            db.session.add(matheus)
            db.session.commit()

            # Create professional profile
            matheus_prof = Professional(
                user_id=matheus.id,
                name='Dr. Matheus Parreira',
                specialty='Psicologia Clínica',
                license_number='CRP-54321',
                phone='(11) 99999-9999'
            )
            db.session.add(matheus_prof)
            db.session.commit()
            print("Dr. Matheus Parreira created")

        print("Database seeded successfully!")

    except Exception as e:
        print(f"Error seeding database: {e}")
        db.session.rollback()