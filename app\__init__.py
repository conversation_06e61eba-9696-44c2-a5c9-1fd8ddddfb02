# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask import Flask
from flask_login import LoginManager
from flask_sqlalchemy import SQLAlchemy
from config import Config

# Initialize extensions
db = SQLAlchemy()

# Initialize login manager
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'

@login_manager.user_loader
def load_user(id):
    from app.models.user import User
    return User.query.get(int(id))

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)

    # Adicionar filtros personalizados
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ""

    # Register blueprints
    from app.controllers.auth_controller import auth_bp
    from app.controllers.professional_controller import professional_bp
    from app.controllers.patient_controller import patient_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(professional_bp)
    app.register_blueprint(patient_bp)

    # Create tables and seed data
    with app.app_context():
        db.create_all()
        seed_database()

    return app

def seed_database():
    """Seed the database with initial data using repositories"""
    try:
        print("🌱 Starting database seed...")

        # Simple seed without complex dependencies
        from werkzeug.security import generate_password_hash
        from app.models.user import User
        from app.models.professional import Professional
        from app.models.schedule import Schedule
        from datetime import time

        # Create admin user
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            print("✅ Admin user created: username=admin, password=admin123")

        # Create professional user
        if not User.query.filter_by(username='profissional').first():
            prof_user = User(
                username='profissional',
                email='<EMAIL>',
                password_hash=generate_password_hash('prof123'),
                role='professional'
            )
            db.session.add(prof_user)
            db.session.flush()  # Get the ID

            # Create professional profile
            professional = Professional(
                user_id=prof_user.id,
                name='Dr. João Silva',
                specialty='Psicologia Clínica',
                license_number='CRP-12345',
                phone='(11) 99999-9999',
                is_accepting_patients=True
            )
            db.session.add(professional)
            db.session.flush()  # Get the ID

            # Create schedules for Monday to Friday
            for day in range(5):  # 0=Monday, 4=Friday
                # Morning schedule
                morning_schedule = Schedule(
                    professional_id=professional.id,
                    day_of_week=day,
                    start_time=time(8, 0),   # 8:00 AM
                    end_time=time(12, 0),    # 12:00 PM
                    slot_duration=60,        # 1 hour slots
                    is_active=True
                )
                db.session.add(morning_schedule)

                # Afternoon schedule
                afternoon_schedule = Schedule(
                    professional_id=professional.id,
                    day_of_week=day,
                    start_time=time(14, 0),  # 2:00 PM
                    end_time=time(18, 0),    # 6:00 PM
                    slot_duration=60,        # 1 hour slots
                    is_active=True
                )
                db.session.add(afternoon_schedule)

            print("✅ Professional created: Dr. João Silva with schedules")

        # Create patient user
        if not User.query.filter_by(username='paciente').first():
            patient_user = User(
                username='paciente',
                email='<EMAIL>',
                password_hash=generate_password_hash('paciente123'),
                role='patient'
            )
            db.session.add(patient_user)
            print("✅ Patient created: paciente")

        db.session.commit()
        print("🎉 Database seeded successfully!")
        print("\n🔑 Demo credentials:")
        print("   Admin: admin / admin123")
        print("   Professional: profissional / prof123")
        print("   Patient: paciente / paciente123")
        print("\n📅 Professional schedules:")
        print("   Monday to Friday: 8:00-12:00 and 14:00-18:00 (1-hour slots)")

    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.session.rollback()