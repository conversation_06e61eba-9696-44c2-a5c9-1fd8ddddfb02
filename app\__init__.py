# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask import Flask
from flask_login import <PERSON>ginManager
from config import Config
from app.models import User, init_db

login_manager = LoginManager()
login_manager.login_view = 'main.login'
login_manager.login_message = 'Please log in to access this page.'

@login_manager.user_loader
def load_user(id):
    return User.get_by_id(int(id))

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    login_manager.init_app(app)

    # Adicionar filtros personalizados
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ""

    # Register blueprints
    from app.routes import main
    app.register_blueprint(main)

    # Initialize database
    init_db()

    # Create mock users for testing

    # Admin user
    admin = User.get_by_username('admin')
    if admin is None:
        admin = User(username='admin', email='<EMAIL>', role='admin')
        admin.set_password('admin123')
        admin.save()
        print("Admin user created: username=admin, password=admin123")

    # Professional user
    prof = User.get_by_username('profissional')
    if prof is None:
        prof = User(username='profissional', email='<EMAIL>', role='professional')
        prof.set_password('prof123')
        prof.save()

        # Create professional profile
        from app.models import Professional, Schedule
        professional = Professional.get_by_user_id(prof.id)
        if professional is None:
            professional = Professional(
                user_id=prof.id,
                name='Dr. Exemplo',
                specialty='Psicologia',
                license_number='12345',
                phone='(11) 98765-4321'
            )
            professional.save()

            # Adicionar horários de exemplo na agenda
            schedules = [
                # Segunda-feira
                Schedule(professional_id=professional.id, day_of_week=0, start_time='09:00', end_time='12:00'),
                Schedule(professional_id=professional.id, day_of_week=0, start_time='14:00', end_time='18:00'),
                # Terça-feira
                Schedule(professional_id=professional.id, day_of_week=1, start_time='09:00', end_time='12:00'),
                Schedule(professional_id=professional.id, day_of_week=1, start_time='14:00', end_time='18:00'),
                # Quarta-feira
                Schedule(professional_id=professional.id, day_of_week=2, start_time='09:00', end_time='12:00'),
                # Quinta-feira
                Schedule(professional_id=professional.id, day_of_week=3, start_time='14:00', end_time='18:00'),
                # Sexta-feira
                Schedule(professional_id=professional.id, day_of_week=4, start_time='09:00', end_time='12:00'),
                Schedule(professional_id=professional.id, day_of_week=4, start_time='14:00', end_time='18:00'),
            ]

            for schedule in schedules:
                schedule.save()

        print("Professional user created: username=profissional, password=prof123")

    # Patient user
    patient_user = User.get_by_username('paciente')
    if patient_user is None:
        patient_user = User(username='paciente', email='<EMAIL>', role='patient')
        patient_user.set_password('paciente123')
        patient_user.save()

        # Create patient profile
        from app.models import Patient
        patient = Patient.get_by_user_id(patient_user.id)
        if patient is None:
            patient = Patient(
                user_id=patient_user.id,
                name='João Paciente',
                email='<EMAIL>',
                phone='(11) 91234-5678',
                date_of_birth='1990-01-01',
                address='Rua dos Pacientes, 123'
            )
            patient.save()
        print("Patient user created: username=paciente, password=paciente123")

        # Create mock appointments for testing
        from app.models import Appointment
        from datetime import datetime, timedelta
        import hashlib

        # Get professional and patient
        prof_user = User.get_by_username('profissional')
        pat_user = User.get_by_username('paciente')

        if prof_user and pat_user:
            professional = Professional.get_by_user_id(prof_user.id)
            patient = Patient.get_by_user_id(pat_user.id)

            if professional and patient:
                # Create past appointments with observations
                today = datetime.now().date()

                # Appointment 1 - 7 days ago
                past_date_1 = (today - timedelta(days=7)).strftime('%Y-%m-%d')
                # Gerar um link fictício do Google Meet para evitar problemas de contexto de requisição
                meeting_id_1 = hashlib.md5(f"{professional.id}-{patient.id}-{past_date_1}-10:00".encode()).hexdigest()[:10]
                meet_link_1 = f"https://meet.google.com/{meeting_id_1}-{past_date_1.replace('-', '')}"

                appointment1 = Appointment(
                    professional_id=professional.id,
                    patient_id=patient.id,
                    appointment_date=past_date_1,
                    start_time='10:00',
                    end_time='11:00',
                    status='completed',
                    notes='Paciente relatou dores de cabeça frequentes',
                    meeting_link=meet_link_1,
                    post_appointment_notes='Paciente apresentou sintomas de enxaqueca. Recomendei redução do tempo de tela e exercícios de relaxamento. Agendamos retorno em 15 dias para acompanhamento.'
                )
                appointment1.save()

                # Appointment 2 - 14 days ago
                past_date_2 = (today - timedelta(days=14)).strftime('%Y-%m-%d')
                # Gerar um link fictício do Google Meet para evitar problemas de contexto de requisição
                meeting_id_2 = hashlib.md5(f"{professional.id}-{patient.id}-{past_date_2}-14:00".encode()).hexdigest()[:10]
                meet_link_2 = f"https://meet.google.com/{meeting_id_2}-{past_date_2.replace('-', '')}"

                appointment2 = Appointment(
                    professional_id=professional.id,
                    patient_id=patient.id,
                    appointment_date=past_date_2,
                    start_time='14:00',
                    end_time='15:00',
                    status='completed',
                    notes='Primeira consulta - avaliação inicial',
                    meeting_link=meet_link_2,
                    post_appointment_notes='Realizamos a avaliação inicial. Paciente relatou histórico de ansiedade e dificuldades de concentração. Estabelecemos plano terapêutico inicial com foco em técnicas de mindfulness e organização de rotina.'
                )
                appointment2.save()

                # Appointment 3 - Future appointment
                future_date = (today + timedelta(days=7)).strftime('%Y-%m-%d')
                # Gerar um link fictício do Google Meet para evitar problemas de contexto de requisição
                meeting_id_3 = hashlib.md5(f"{professional.id}-{patient.id}-{future_date}-15:00".encode()).hexdigest()[:10]
                meet_link_3 = f"https://meet.google.com/{meeting_id_3}-{future_date.replace('-', '')}"

                appointment3 = Appointment(
                    professional_id=professional.id,
                    patient_id=patient.id,
                    appointment_date=future_date,
                    start_time='15:00',
                    end_time='16:00',
                    status='scheduled',
                    notes='Consulta de acompanhamento',
                    meeting_link=meet_link_3
                )
                appointment3.save()

                print("Mock appointments created for testing")

    return app
