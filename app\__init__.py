# Permitir OAuth2 em HTTP para ambiente de desenvolvimento
import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask import Flask
from flask_login import LoginManager
import sqlite3
import os
from config import Config

# Simple database connection
DATABASE_PATH = 'gestao_terapia.db'

# Initialize login manager
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'

@login_manager.user_loader
def load_user(id):
    # Simple user loading for demo
    return None

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize extensions
    login_manager.init_app(app)

    # Initialize database
    init_database()

    # Adicionar filtros personalizados
    @app.template_filter('nl2br')
    def nl2br(value):
        if value:
            return value.replace('\n', '<br>')
        return ""

    # Register blueprints
    from app.controllers.auth_controller import auth_bp
    from app.controllers.professional_controller import professional_bp
    from app.controllers.patient_controller import patient_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(professional_bp)
    app.register_blueprint(patient_bp)

    return app

def init_database():
    """Initialize SQLite database with simple tables"""
    if not os.path.exists(DATABASE_PATH):
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Create simple tables for demo
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'patient',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE professionals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                name TEXT NOT NULL,
                specialty TEXT,
                license_number TEXT,
                phone TEXT,
                is_accepting_patients BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        cursor.execute('''
            CREATE TABLE schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                professional_id INTEGER,
                day_of_week INTEGER,
                start_time TEXT,
                end_time TEXT,
                slot_duration INTEGER DEFAULT 60,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (professional_id) REFERENCES professionals (id)
            )
        ''')

        # Insert demo data
        from werkzeug.security import generate_password_hash

        # Demo professional
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', ('profissional', '<EMAIL>', generate_password_hash('prof123'), 'professional'))

        user_id = cursor.lastrowid

        cursor.execute('''
            INSERT INTO professionals (user_id, name, specialty, license_number, phone)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, 'Dr. João Silva', 'Psicologia Clínica', 'CRP-12345', '(11) 99999-9999'))

        prof_id = cursor.lastrowid

        # Demo schedules (Monday to Friday, 8-12 and 14-18)
        for day in range(5):
            cursor.execute('''
                INSERT INTO schedules (professional_id, day_of_week, start_time, end_time)
                VALUES (?, ?, ?, ?)
            ''', (prof_id, day, '08:00', '12:00'))

            cursor.execute('''
                INSERT INTO schedules (professional_id, day_of_week, start_time, end_time)
                VALUES (?, ?, ?, ?)
            ''', (prof_id, day, '14:00', '18:00'))

        # Demo patient
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', ('paciente', '<EMAIL>', generate_password_hash('paciente123'), 'patient'))

        conn.commit()
        conn.close()

        print("✅ Demo database created successfully!")
        print("🔑 Demo credentials:")
        print("   Professional: profissional / prof123")
        print("   Patient: paciente / paciente123")