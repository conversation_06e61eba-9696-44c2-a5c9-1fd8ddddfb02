"""
Professional Repository - Single Responsibility Principle
Responsável pelo acesso aos dados de profissionais
"""

from typing import Optional, List
from app.models.professional import Professional
from .base_repository import BaseRepository


class ProfessionalRepository(BaseRepository):
    """Repositório para operações específicas de profissionais"""
    
    def __init__(self):
        super().__init__(Professional)
    
    def get_by_user_id(self, user_id: int) -> Optional[Professional]:
        """Busca profissional por ID do usuário"""
        return self.find_one_by(user_id=user_id)
    
    def get_by_license_number(self, license_number: str) -> Optional[Professional]:
        """Busca profissional por número de registro"""
        return self.find_one_by(license_number=license_number)
    
    def get_by_specialty(self, specialty: str) -> List[Professional]:
        """Busca profissionais por especialidade"""
        return self.find_by(specialty=specialty)
    
    def get_accepting_patients(self) -> List[Professional]:
        """Retorna profissionais que estão aceitando pacientes"""
        return self.find_by(is_accepting_patients=True)
    
    def search_by_name(self, name: str) -> List[Professional]:
        """Busca profissionais por nome"""
        search_pattern = f"%{name}%"
        return Professional.query.filter(
            Professional.name.ilike(search_pattern)
        ).all()
    
    def get_with_patient_count(self) -> List[dict]:
        """Retorna profissionais com contagem de pacientes"""
        from app.models.patient import Patient
        from app import db
        
        return db.session.query(
            Professional,
            db.func.count(Patient.id).label('patient_count')
        ).outerjoin(Patient).group_by(Professional.id).all()
    
    def get_repository_specific_methods(self):
        return [
            'get_by_user_id', 'get_by_license_number', 'get_by_specialty',
            'get_accepting_patients', 'search_by_name', 'get_with_patient_count'
        ]
