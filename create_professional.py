#!/usr/bin/env python3
"""
Script para criar um profissional com email específico para teste
"""

import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models import User, Professional, init_db

def create_professional_with_email():
    """Cria um profissional com <NAME_EMAIL>"""

    # Inicializar o banco de dados
    init_db()

    # Dados do profissional
    email = "<EMAIL>"
    username = "matheus_parreira"
    password = "teste123"
    name = "Dr. <PERSON>"
    specialty = "Psicologia Clínica"
    license_number = "CRP-12345"
    phone = "(11) 99999-9999"

    # Verificar se o usuário já existe
    existing_user = User.get_by_email(email)
    if existing_user:
        print(f"❌ Usuário com email {email} já existe!")

        # Verificar se já tem profissional associado
        existing_professional = Professional.get_by_user_id(existing_user.id)
        if existing_professional:
            print(f"✅ Profissional já existe: {existing_professional.name}")
            print(f"📧 Email: {email}")
            print(f"👤 Username: {existing_user.username}")
            print(f"🔑 Senha: {password}")
            return existing_professional
        else:
            # Criar apenas o profissional
            professional = Professional(
                user_id=existing_user.id,
                name=name,
                specialty=specialty,
                license_number=license_number,
                phone=phone
            )
            professional.save()
            print(f"✅ Profissional criado para usuário existente: {professional.name}")
            return professional

    # Verificar se o username já existe
    existing_username = User.get_by_username(username)
    if existing_username:
        username = f"{username}_test"
        print(f"⚠️ Username alterado para: {username}")

    try:
        # Criar o usuário
        user = User(
            username=username,
            email=email,
            role='professional'
        )
        user.set_password(password)
        user.save()

        print(f"✅ Usuário criado: {user.username}")

        # Criar o profissional
        professional = Professional(
            user_id=user.id,
            name=name,
            specialty=specialty,
            license_number=license_number,
            phone=phone
        )
        professional.save()

        print(f"✅ Profissional criado com sucesso!")
        print(f"📧 Email: {email}")
        print(f"👤 Username: {username}")
        print(f"🔑 Senha: {password}")
        print(f"👨‍⚕️ Nome: {name}")
        print(f"🏥 Especialidade: {specialty}")
        print(f"📋 Registro: {license_number}")
        print(f"📞 Telefone: {phone}")

        return professional

    except Exception as e:
        print(f"❌ Erro ao criar profissional: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Criando profissional para teste...")
    professional = create_professional_with_email()

    if professional:
        print("\n🎉 Profissional criado com sucesso!")
        print("\n📝 Credenciais para login:")
        print(f"   Email/Username: matheus_parreira (ou matheus_parreira_test)")
        print(f"   Senha: teste123")
        print("\n🌐 Acesse: http://127.0.0.1:5000/login")
    else:
        print("\n❌ Falha ao criar profissional!")
