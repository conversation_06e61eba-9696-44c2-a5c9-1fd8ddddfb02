<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestão Terapia{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-md navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-brain me-2"></i>Gestão Terapia
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i> Home
                        </a>
                    </li>
                    {% if current_user.is_authenticated %}
                        {% if current_user.is_admin() %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.admin_dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                                </a>
                            </li>
                        {% elif current_user.is_professional() %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.professional_dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.professional_schedule') }}">
                                    <i class="fas fa-calendar-alt me-1"></i> Minha Agenda
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.professional_appointments') }}">
                                    <i class="fas fa-calendar-check me-1"></i> Agendamentos
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('main.professional_consultations') }}">
                                    <i class="fas fa-clipboard-list me-1"></i> Consultas
                                </a>
                            </li>
                        {% endif %}
                    {% elif current_user.is_patient is defined and current_user.is_patient() %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.patient_dashboard') }}">
                                <i class="fas fa-tachometer-alt me-1"></i> Meu Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.view_professionals') }}">
                                <i class="fas fa-user-md me-1"></i> Profissionais
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.view_professionals') }}">
                                <i class="fas fa-user-md me-1"></i> Profissionais
                            </a>
                        </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item d-flex align-items-center me-3">
                        <label class="theme-toggle mb-0" title="Alternar modo escuro">
                            <input type="checkbox" id="theme-toggle">
                            <span class="theme-toggle-slider">
                                <i class="fas fa-sun theme-toggle-icon sun"></i>
                                <i class="fas fa-moon theme-toggle-icon moon"></i>
                            </span>
                        </label>
                    </li>
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <span class="nav-link">
                                <i class="fas fa-user-circle me-1"></i> {{ current_user.username }}
                            </span>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i> Logout
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('main.login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i> Login
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="registerDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-plus me-1"></i> Registrar
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="registerDropdown">
                                <li><a class="dropdown-item" href="{{ url_for('main.register') }}">
                                    <i class="fas fa-user-tie me-1"></i> Admin/Profissional
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('main.patient_register') }}">
                                    <i class="fas fa-user me-1"></i> Paciente
                                </a></li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container fade-in">
        <!-- Flash Messages -->
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        {% if category == 'error' %}
                            {% set alert_class = 'alert-danger' %}
                            {% set icon_class = 'fas fa-exclamation-circle' %}
                        {% elif category == 'success' %}
                            {% set alert_class = 'alert-success' %}
                            {% set icon_class = 'fas fa-check-circle' %}
                        {% elif category == 'warning' %}
                            {% set alert_class = 'alert-warning' %}
                            {% set icon_class = 'fas fa-exclamation-triangle' %}
                        {% else %}
                            {% set alert_class = 'alert-info' %}
                            {% set icon_class = 'fas fa-info-circle' %}
                        {% endif %}
                        <div class="alert {{ alert_class }} alert-dismissible fade show">
                            <i class="{{ icon_class }} me-2"></i> {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">© 2025 Gestão Terapia - Todos os direitos reservados</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script>
        // Ativar tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Destacar o item de menu ativo
            const currentLocation = window.location.pathname;
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentLocation) {
                    link.classList.add('active');
                }
            });

            // Dark Mode Toggle
            const themeToggle = document.getElementById('theme-toggle');
            const htmlElement = document.documentElement;

            // Verificar preferência salva
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                htmlElement.setAttribute('data-theme', 'dark');
                themeToggle.checked = true;
            }

            // Verificar preferência do sistema se não houver preferência salva
            if (!savedTheme) {
                const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (prefersDarkMode) {
                    htmlElement.setAttribute('data-theme', 'dark');
                    themeToggle.checked = true;
                    localStorage.setItem('theme', 'dark');
                }
            }

            // Alternar tema
            themeToggle.addEventListener('change', function() {
                if (this.checked) {
                    htmlElement.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                } else {
                    htmlElement.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
