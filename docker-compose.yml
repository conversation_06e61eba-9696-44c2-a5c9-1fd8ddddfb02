version: '3.8'

services:
  # Banco de dados PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: gestao_terapia_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: gestao_terapia
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - gestao_terapia_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d gestao_terapia"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Aplicação Flask
  web:
    build: .
    container_name: gestao_terapia_web
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URL=*****************************************/gestao_terapia
      - SECRET_KEY=dev-key-for-development-only
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_REDIRECT_URI=http://localhost:5000/oauth2callback
    volumes:
      - .:/app
      - /app/venv
    depends_on:
      db:
        condition: service_healthy
    networks:
      - gestao_terapia_network
    command: python run.py

  # Adminer para gerenciamento do banco (opcional)
  adminer:
    image: adminer:latest
    container_name: gestao_terapia_adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      - db
    networks:
      - gestao_terapia_network
    environment:
      ADMINER_DEFAULT_SERVER: db

volumes:
  postgres_data:
    driver: local

networks:
  gestao_terapia_network:
    driver: bridge
