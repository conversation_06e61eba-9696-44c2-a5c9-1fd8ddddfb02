"""
Professional Model - Single Responsibility Principle
Entidade de domínio responsável pela representação do profissional
"""

from app import db
from .base_model import BaseModel


class Professional(BaseModel):
    """
    Entidade Professional - representa um profissional de saúde
    Responsabilidades:
    - Dados profissionais específicos
    - Relacionamentos com pacientes e agendamentos
    - Validações de negócio específicas
    """
    __tablename__ = 'professionals'
    
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    name = db.Column(db.String(100), nullable=False)
    specialty = db.Column(db.String(100), nullable=False)
    license_number = db.Column(db.String(50), nullable=False, unique=True)
    phone = db.Column(db.String(20))
    bio = db.Column(db.Text)
    is_accepting_patients = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Relacionamentos
    patients = db.relationship('Patient', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    schedules = db.relationship('Schedule', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    appointments = db.relationship('Appointment', backref='professional', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        """Inicializa o profissional com validações"""
        super().__init__(**kwargs)
        self._validate_license_number()
    
    def _validate_license_number(self):
        """Valida o número de registro profissional"""
        if not self.license_number:
            raise ValueError("License number is required")
        
        # Validação básica do formato (pode ser expandida conforme necessário)
        if len(self.license_number) < 5:
            raise ValueError("License number must be at least 5 characters")
    
    def get_patients_count(self) -> int:
        """Retorna o número total de pacientes"""
        return self.patients.count()
    
    def get_active_patients(self):
        """Retorna pacientes ativos"""
        return self.patients.filter_by(is_active=True).all()
    
    def get_appointments_count(self) -> int:
        """Retorna o número total de agendamentos"""
        return self.appointments.count()
    
    def get_pending_appointments(self):
        """Retorna agendamentos pendentes"""
        return self.appointments.filter_by(status='scheduled').all()
    
    def get_completed_appointments(self):
        """Retorna agendamentos concluídos"""
        return self.appointments.filter_by(status='completed').all()
    
    def has_schedule_for_day(self, day_of_week: int) -> bool:
        """Verifica se tem agenda configurada para um dia específico"""
        return self.schedules.filter_by(day_of_week=day_of_week).count() > 0
    
    def can_accept_new_patients(self) -> bool:
        """Verifica se pode aceitar novos pacientes"""
        return self.is_accepting_patients and self.get_patients_count() < 100  # Limite configurável
    
    def toggle_accepting_patients(self) -> None:
        """Alterna o status de aceitar novos pacientes"""
        self.is_accepting_patients = not self.is_accepting_patients
        self.save()
    
    def update_bio(self, bio: str) -> None:
        """Atualiza a biografia do profissional"""
        self.bio = bio
        self.save()
    
    def update_contact_info(self, phone: str = None) -> None:
        """Atualiza informações de contato"""
        if phone:
            self.phone = phone
        self.save()
    
    def get_statistics(self) -> dict:
        """Retorna estatísticas do profissional"""
        return {
            'total_patients': self.get_patients_count(),
            'active_patients': len(self.get_active_patients()),
            'total_appointments': self.get_appointments_count(),
            'pending_appointments': len(self.get_pending_appointments()),
            'completed_appointments': len(self.get_completed_appointments()),
            'accepting_patients': self.is_accepting_patients
        }
    
    def to_dict(self):
        """Converte para dicionário incluindo estatísticas básicas"""
        data = super().to_dict()
        data['statistics'] = self.get_statistics()
        return data
    
    def __repr__(self):
        return f'<Professional {self.name} ({self.specialty})>'
