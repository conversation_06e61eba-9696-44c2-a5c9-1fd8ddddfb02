{% extends "base.html" %}

{% block title %}My Schedule - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><PERSON><PERSON></h2>
    <a href="{{ url_for('main.add_schedule') }}" class="btn btn-primary"><PERSON><PERSON><PERSON><PERSON></a>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><PERSON><PERSON><PERSON><PERSON><PERSON> Disponíveis</h5>
    </div>
    <div class="card-body">
        {% set days = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo'] %}
        
        <div class="accordion" id="scheduleAccordion">
            {% for day_index, schedules in schedule_by_day.items() %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ day_index }}">
                        <button class="accordion-button {% if not schedules %}collapsed{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ day_index }}" aria-expanded="{{ 'true' if schedules else 'false' }}" aria-controls="collapse{{ day_index }}">
                            {{ days[day_index] }}
                            <span class="badge bg-primary ms-2">{{ schedules|length }}</span>
                        </button>
                    </h2>
                    <div id="collapse{{ day_index }}" class="accordion-collapse collapse {% if schedules %}show{% endif %}" aria-labelledby="heading{{ day_index }}" data-bs-parent="#scheduleAccordion">
                        <div class="accordion-body">
                            {% if schedules %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Horário de Início</th>
                                                <th>Horário de Término</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for schedule in schedules %}
                                                <tr>
                                                    <td>{{ schedule.start_time }}</td>
                                                    <td>{{ schedule.end_time }}</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="{{ url_for('main.edit_schedule', id=schedule.id) }}" class="btn btn-outline-primary">Editar</a>
                                                            <a href="{{ url_for('main.delete_schedule', id=schedule.id) }}" class="btn btn-outline-danger" onclick="return confirm('Tem certeza que deseja excluir este horário?')">Excluir</a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    Nenhum horário definido para {{ days[day_index] }}.
                                    <a href="{{ url_for('main.add_schedule') }}" class="alert-link">Adicionar horário</a>.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<div class="alert alert-info">
    <h5>Dicas:</h5>
    <ul>
        <li>Defina seus horários de atendimento para cada dia da semana.</li>
        <li>Os pacientes poderão ver sua disponibilidade e agendar consultas nos horários disponíveis.</li>
        <li>Você pode adicionar múltiplos períodos para o mesmo dia (ex: manhã e tarde).</li>
    </ul>
</div>
{% endblock %}
