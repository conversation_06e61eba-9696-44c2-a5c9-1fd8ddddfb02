{% extends "base.html" %}

{% block title %}Meus Agendamentos - Sistema de Gestão Terapêutica{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-calendar-check me-2"></i>Meus Agendamentos</h2>
    <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary">
        <i class="fas fa-calendar-plus me-2"></i>Novo Agendamento
    </a>
</div>

{% if appointments %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Lista de Agendamentos ({{ appointments|length }})</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th><PERSON><PERSON><PERSON><PERSON></th>
                        <th>Profissional</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments %}
                    <tr>
                        <td>{{ appointment.appointment_date.strftime('%d/%m/%Y') }}</td>
                        <td>
                            {{ appointment.start_time.strftime('%H:%M') }} - 
                            {{ appointment.end_time.strftime('%H:%M') }}
                        </td>
                        <td>
                            <i class="fas fa-user-md me-2"></i>
                            {{ appointment.get_professional_name() }}
                        </td>
                        <td>
                            {% set status_colors = {
                                'scheduled': 'primary',
                                'confirmed': 'success', 
                                'completed': 'info',
                                'cancelled': 'danger',
                                'in_progress': 'warning'
                            } %}
                            <span class="badge bg-{{ status_colors.get(appointment.status, 'secondary') }}">
                                {{ appointment.get_status_display() }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                {% if appointment.status in ['scheduled', 'confirmed'] and appointment.is_upcoming() %}
                                    <button class="btn btn-outline-success" onclick="alert('Funcionalidade em desenvolvimento')">
                                        <i class="fas fa-video"></i> Entrar
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="alert('Funcionalidade em desenvolvimento')">
                                        <i class="fas fa-edit"></i> Reagendar
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="alert('Funcionalidade em desenvolvimento')">
                                        <i class="fas fa-times"></i> Cancelar
                                    </button>
                                {% else %}
                                    <button class="btn btn-outline-info" onclick="alert('Funcionalidade em desenvolvimento')">
                                        <i class="fas fa-eye"></i> Ver Detalhes
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
        <h4>Nenhum agendamento encontrado</h4>
        <p class="text-muted">Você ainda não possui agendamentos marcados.</p>
        <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary">
            <i class="fas fa-calendar-plus me-2"></i>Agendar Primeira Consulta
        </a>
    </div>
</div>
{% endif %}
{% endblock %}
