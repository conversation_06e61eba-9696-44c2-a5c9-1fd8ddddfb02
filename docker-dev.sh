#!/bin/bash

# Script para facilitar o desenvolvimento com Docker

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Verificar se Docker está instalado
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker não está instalado. Por favor, instale o Docker primeiro."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
        exit 1
    fi
}

# Função para iniciar os serviços
start_services() {
    print_header "Iniciando Serviços Docker"
    
    print_message "Parando containers existentes..."
    docker-compose down
    
    print_message "Construindo e iniciando containers..."
    docker-compose up --build -d
    
    print_message "Aguardando banco de dados ficar pronto..."
    sleep 10
    
    print_message "Verificando status dos containers..."
    docker-compose ps
    
    print_message "Serviços iniciados com sucesso!"
    echo ""
    echo "🌐 Aplicação: http://localhost:5000"
    echo "🗄️  Adminer (DB Admin): http://localhost:8080"
    echo "📊 PostgreSQL: localhost:5432"
    echo ""
    echo "Credenciais do banco:"
    echo "  Database: gestao_terapia"
    echo "  Username: postgres"
    echo "  Password: postgres123"
}

# Função para parar os serviços
stop_services() {
    print_header "Parando Serviços Docker"
    docker-compose down
    print_message "Serviços parados com sucesso!"
}

# Função para ver logs
view_logs() {
    print_header "Visualizando Logs"
    docker-compose logs -f
}

# Função para executar comandos no container da aplicação
exec_app() {
    print_message "Executando comando no container da aplicação..."
    docker-compose exec web "$@"
}

# Função para acessar o shell do container
shell() {
    print_message "Acessando shell do container da aplicação..."
    docker-compose exec web /bin/bash
}

# Função para executar migrações
migrate() {
    print_header "Executando Migrações do Banco"
    docker-compose exec web flask db upgrade
}

# Função para criar nova migração
create_migration() {
    print_header "Criando Nova Migração"
    if [ -z "$1" ]; then
        print_error "Por favor, forneça uma mensagem para a migração."
        echo "Uso: $0 create-migration 'mensagem da migração'"
        exit 1
    fi
    docker-compose exec web flask db migrate -m "$1"
}

# Função para resetar o banco de dados
reset_db() {
    print_header "Resetando Banco de Dados"
    print_warning "ATENÇÃO: Isso irá apagar todos os dados do banco!"
    read -p "Tem certeza? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down
        docker volume rm gestao_terapia_postgres_data 2>/dev/null || true
        docker-compose up -d db
        sleep 10
        docker-compose up -d web
        print_message "Banco de dados resetado com sucesso!"
    else
        print_message "Operação cancelada."
    fi
}

# Função para mostrar ajuda
show_help() {
    echo "Uso: $0 [COMANDO]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  start              Iniciar todos os serviços"
    echo "  stop               Parar todos os serviços"
    echo "  restart            Reiniciar todos os serviços"
    echo "  logs               Visualizar logs em tempo real"
    echo "  shell              Acessar shell do container da aplicação"
    echo "  exec [comando]     Executar comando no container da aplicação"
    echo "  migrate            Executar migrações do banco"
    echo "  create-migration   Criar nova migração"
    echo "  reset-db           Resetar banco de dados (CUIDADO!)"
    echo "  help               Mostrar esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 start"
    echo "  $0 logs"
    echo "  $0 exec python -c 'print(\"Hello World\")'"
    echo "  $0 create-migration 'Adicionar tabela de comentários'"
}

# Verificar Docker
check_docker

# Processar argumentos
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        stop_services
        start_services
        ;;
    logs)
        view_logs
        ;;
    shell)
        shell
        ;;
    exec)
        shift
        exec_app "$@"
        ;;
    migrate)
        migrate
        ;;
    create-migration)
        create_migration "$2"
        ;;
    reset-db)
        reset_db
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Comando desconhecido: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
