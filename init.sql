-- Script de inicialização do banco PostgreSQL
-- Este arquivo é executado automaticamente quando o container do PostgreSQL é criado

-- Criar extensões úteis
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Configurações de timezone
SET timezone = 'America/Sao_Paulo';

-- Criar índices adicionais para performance (serão criados após as tabelas)
-- Os índices principais já são criados pelo SQLAlchemy
