"""
Patient Controller - Single Responsibility Principle
Responsável pelas rotas específicas de pacientes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, time
from app.repositories.patient_repository import PatientRepository
from app.repositories.professional_repository import ProfessionalRepository
from app.repositories.appointment_repository import AppointmentRepository
from app.services.appointment_service import AppointmentService
from app.services.schedule_service import ScheduleService

# Create blueprint
patient_bp = Blueprint('patient', __name__, url_prefix='/patient')

# Initialize repositories and services
patient_repo = PatientRepository()
prof_repo = ProfessionalRepository()
appointment_repo = AppointmentRepository()
appointment_service = AppointmentService()
schedule_service = ScheduleService()


@patient_bp.route('/dashboard')
@login_required
def dashboard():
    """Dashboard do paciente"""
    if not current_user.is_patient():
        flash('Acesso negado. Apenas pacientes.', 'error')
        return redirect(url_for('auth.dashboard'))

    # Buscar perfil do paciente
    patient = patient_repo.get_by_user_id(current_user.id)
    if not patient:
        flash('Perfil de paciente não encontrado.', 'error')
        return redirect(url_for('auth.logout'))

    # Buscar agendamentos
    upcoming_appointments = appointment_repo.get_upcoming_appointments(patient_id=patient.id)

    return render_template('patient/dashboard.html',
                         patient=patient,
                         upcoming_appointments=upcoming_appointments)


@patient_bp.route('/appointments')
@login_required
def appointments():
    """Lista de agendamentos do paciente"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))

    patient = patient_repo.get_by_user_id(current_user.id)
    if not patient:
        flash('Perfil de paciente não encontrado.', 'error')
        return redirect(url_for('auth.logout'))

    appointments = appointment_repo.get_by_patient_id(patient.id)

    return render_template('patient/appointments.html',
                         patient=patient,
                         appointments=appointments)


@patient_bp.route('/professionals')
@login_required
def professionals():
    """Lista de profissionais disponíveis"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))

    professionals = prof_repo.get_accepting_patients()

    return render_template('patient/professionals.html',
                         professionals=professionals)


@patient_bp.route('/professional/<int:professional_id>/schedule')
@login_required
def professional_schedule(professional_id):
    """Visualizar agenda de um profissional"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))

    professional = prof_repo.get_by_id(professional_id)
    if not professional:
        flash('Profissional não encontrado.', 'error')
        return redirect(url_for('patient.professionals'))

    # Buscar horários do profissional
    schedules = schedule_service.get_professional_schedules(professional_id)

    # Organizar schedules por dia da semana
    schedule_by_day = {}
    for schedule in schedules:
        day = schedule.day_of_week
        if day not in schedule_by_day:
            schedule_by_day[day] = []
        schedule_by_day[day].append(schedule)

    return render_template('patient/professional_schedule.html',
                         professional=professional,
                         schedules=schedules,
                         schedule_by_day=schedule_by_day)


@patient_bp.route('/professional/<int:professional_id>/available-slots')
@login_required
def available_slots(professional_id):
    """API para buscar slots disponíveis"""
    if not current_user.is_patient():
        return jsonify({'error': 'Acesso negado'}), 403

    date_str = request.args.get('date')
    if not date_str:
        return jsonify({'error': 'Data é obrigatória'}), 400

    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'error': 'Formato de data inválido'}), 400

    slots = appointment_service.get_available_slots(professional_id, target_date)

    return jsonify({
        'slots': [
            {
                'start_time': slot['start_time'].strftime('%H:%M'),
                'end_time': slot['end_time'].strftime('%H:%M'),
                'available': slot['available']
            }
            for slot in slots
        ]
    })


@patient_bp.route('/book-appointment', methods=['GET', 'POST'])
@login_required
def book_appointment():
    """Agendar consulta"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))

    patient = patient_repo.get_by_user_id(current_user.id)
    if not patient:
        flash('Perfil de paciente não encontrado.', 'error')
        return redirect(url_for('auth.logout'))

    if request.method == 'POST':
        try:
            professional_id = int(request.form.get('professional_id'))
            appointment_date = datetime.strptime(request.form.get('appointment_date'), '%Y-%m-%d').date()
            start_time = datetime.strptime(request.form.get('start_time'), '%H:%M').time()
            end_time = datetime.strptime(request.form.get('end_time'), '%H:%M').time()
            notes = request.form.get('notes', '')

            # Criar agendamento
            appointment = appointment_service.create_appointment(
                professional_id=professional_id,
                patient_id=patient.id,
                appointment_date=appointment_date,
                start_time=start_time,
                end_time=end_time,
                notes=notes
            )

            flash('Agendamento criado com sucesso!', 'success')
            return redirect(url_for('patient.appointments'))

        except ValueError as e:
            flash(str(e), 'error')
        except Exception as e:
            flash(f'Erro ao criar agendamento: {str(e)}', 'error')

    # GET request
    professional_id = request.args.get('professional_id')
    professional = None
    if professional_id:
        professional = prof_repo.get_by_id(int(professional_id))

    professionals = prof_repo.get_accepting_patients()

    return render_template('patient/book_appointment.html',
                         professionals=professionals,
                         selected_professional=professional)
