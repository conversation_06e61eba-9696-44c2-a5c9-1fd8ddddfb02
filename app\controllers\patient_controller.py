"""
Patient Controller - Single Responsibility Principle
Responsável pelas rotas específicas de pacientes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from app.repositories.patient_repository import PatientRepository
from app.repositories.professional_repository import ProfessionalRepository
from app.repositories.appointment_repository import AppointmentRepository

# Create blueprint
patient_bp = Blueprint('patient', __name__, url_prefix='/patient')

# Initialize repositories
patient_repo = PatientRepository()
prof_repo = ProfessionalRepository()
appointment_repo = AppointmentRepository()


@patient_bp.route('/dashboard')
@login_required
def dashboard():
    """Dashboard do paciente"""
    if not current_user.is_patient():
        flash('Acesso negado. Apenas pacientes.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    # Buscar perfil do paciente
    patient = patient_repo.get_by_user_id(current_user.id)
    if not patient:
        flash('Perfil de paciente não encontrado.', 'error')
        return redirect(url_for('auth.logout'))
    
    # Buscar agendamentos
    upcoming_appointments = appointment_repo.get_upcoming_appointments(patient_id=patient.id)
    
    return render_template('patient/dashboard.html',
                         patient=patient,
                         upcoming_appointments=upcoming_appointments)


@patient_bp.route('/appointments')
@login_required
def appointments():
    """Lista de agendamentos do paciente"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    patient = patient_repo.get_by_user_id(current_user.id)
    if not patient:
        flash('Perfil de paciente não encontrado.', 'error')
        return redirect(url_for('auth.logout'))
    
    appointments = appointment_repo.get_by_patient_id(patient.id)
    
    return render_template('patient/appointments.html',
                         patient=patient,
                         appointments=appointments)


@patient_bp.route('/professionals')
@login_required
def professionals():
    """Lista de profissionais disponíveis"""
    if not current_user.is_patient():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    professionals = prof_repo.get_accepting_patients()
    
    return render_template('patient/professionals.html',
                         professionals=professionals)
