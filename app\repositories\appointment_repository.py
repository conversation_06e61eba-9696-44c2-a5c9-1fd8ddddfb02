"""
Appointment Repository - Single Responsibility Principle
Responsável pelo acesso aos dados de agendamentos
"""

from typing import List, Optional
from datetime import date, datetime
from app.models.appointment import Appointment
from .base_repository import BaseRepository


class AppointmentRepository(BaseRepository):
    """Repositório para operações específicas de agendamentos"""

    def __init__(self):
        super().__init__(Appointment)

    def get_by_professional_id(self, professional_id: int,
                              start_date: date = None, end_date: date = None) -> List[Appointment]:
        """Busca agendamentos por profissional"""
        query = Appointment.query.filter_by(professional_id=professional_id)

        if start_date:
            query = query.filter(Appointment.appointment_date >= start_date)
        if end_date:
            query = query.filter(Appointment.appointment_date <= end_date)

        return query.order_by(
            Appointment.appointment_date.asc(),
            Appointment.start_time.asc()
        ).all()

    def get_by_patient_id(self, patient_id: int) -> List[Appointment]:
        """Busca agendamentos por paciente"""
        return Appointment.query.filter_by(patient_id=patient_id).order_by(
            Appointment.appointment_date.desc(),
            Appointment.start_time.desc()
        ).all()

    def get_upcoming_appointments(self, professional_id: int = None,
                                 patient_id: int = None) -> List[Appointment]:
        """Busca agendamentos futuros"""
        query = Appointment.query.filter(
            Appointment.appointment_date >= date.today(),
            Appointment.status != 'cancelled'
        )

        if professional_id:
            query = query.filter_by(professional_id=professional_id)
        if patient_id:
            query = query.filter_by(patient_id=patient_id)

        return query.order_by(
            Appointment.appointment_date.asc(),
            Appointment.start_time.asc()
        ).all()

    def get_today_appointments(self, professional_id: int = None) -> List[Appointment]:
        """Busca agendamentos de hoje"""
        query = Appointment.query.filter_by(appointment_date=date.today())

        if professional_id:
            query = query.filter_by(professional_id=professional_id)

        return query.order_by(Appointment.start_time.asc()).all()

    def get_by_status(self, status: str, professional_id: int = None) -> List[Appointment]:
        """Busca agendamentos por status"""
        query = Appointment.query.filter_by(status=status)

        if professional_id:
            query = query.filter_by(professional_id=professional_id)

        return query.order_by(
            Appointment.appointment_date.desc(),
            Appointment.start_time.desc()
        ).all()

    def check_conflicts(self, professional_id: int, appointment_date: date,
                       start_time, end_time, exclude_id: int = None) -> List[Appointment]:
        """Verifica conflitos de agendamento"""
        return Appointment.get_conflicts(
            professional_id, appointment_date, start_time, end_time, exclude_id
        )

    def get_statistics(self, professional_id: int = None) -> dict:
        """Retorna estatísticas de agendamentos"""
        query = Appointment.query

        if professional_id:
            query = query.filter_by(professional_id=professional_id)

        total = query.count()
        scheduled = query.filter_by(status='scheduled').count()
        completed = query.filter_by(status='completed').count()
        cancelled = query.filter_by(status='cancelled').count()

        return {
            'total': total,
            'scheduled': scheduled,
            'completed': completed,
            'cancelled': cancelled,
            'completion_rate': (completed / total * 100) if total > 0 else 0
        }

    def get_by_professional_and_date(self, professional_id: int, target_date: date) -> List[Appointment]:
        """Busca agendamentos de um profissional em uma data específica"""
        return self.find_by(
            professional_id=professional_id,
            appointment_date=target_date
        )

    def get_repository_specific_methods(self):
        return [
            'get_by_professional_id', 'get_by_patient_id', 'get_upcoming_appointments',
            'get_today_appointments', 'get_by_status', 'check_conflicts', 'get_statistics',
            'get_by_professional_and_date'
        ]
