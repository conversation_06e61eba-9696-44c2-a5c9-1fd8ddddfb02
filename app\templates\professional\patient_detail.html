{% extends "base.html" %}

{% block title %}Detalhes do Paciente - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <h2>Detalhes do Paciente</h2>
            <div>
                <a href="{{ url_for('main.edit_patient', id=patient.id) }}" class="btn btn-primary">Editar Paciente</a>
                <a href="{{ url_for('main.professional_dashboard') }}" class="btn btn-outline-secondary">Voltar</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Informações Pessoais</h5>
            </div>
            <div class="card-body">
                <p><strong>Nome:</strong> {{ patient.name }}</p>
                <p><strong>Email:</strong> {{ patient.email }}</p>
                <p><strong>Telefone:</strong> {{ patient.phone }}</p>
                <p><strong>Data de Nascimento:</strong> {{ patient.date_of_birth }}</p>
                <p><strong>Endereço:</strong> {{ patient.address }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Histórico de Consultas</h5>
            </div>
            <div class="card-body">
                {% if appointments %}
                    <div class="accordion" id="appointmentsAccordion">
                        {% for appointment in appointments %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ appointment.id }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ appointment.id }}" aria-expanded="false" aria-controls="collapse{{ appointment.id }}">
                                        {{ appointment.appointment_date }} ({{ appointment.start_time }} - {{ appointment.end_time }})
                                        {% if appointment.status == 'scheduled' %}
                                            <span class="badge bg-primary ms-2">Agendado</span>
                                        {% elif appointment.status == 'completed' %}
                                            <span class="badge bg-success ms-2">Concluído</span>
                                        {% elif appointment.status == 'cancelled' %}
                                            <span class="badge bg-danger ms-2">Cancelado</span>
                                        {% endif %}
                                    </button>
                                </h2>
                                <div id="collapse{{ appointment.id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ appointment.id }}" data-bs-parent="#appointmentsAccordion">
                                    <div class="accordion-body">
                                        {% if appointment.notes %}
                                            <div class="mb-3">
                                                <h6>Notas Pré-Consulta:</h6>
                                                <p class="mb-0">{{ appointment.notes }}</p>
                                            </div>
                                        {% endif %}
                                        
                                        {% if appointment.status == 'completed' %}
                                            {% if appointment.post_appointment_notes %}
                                                <div class="mb-3">
                                                    <h6>Observações da Consulta:</h6>
                                                    <p class="mb-0">{{ appointment.post_appointment_notes }}</p>
                                                </div>
                                            {% else %}
                                                <div class="alert alert-warning mb-0">
                                                    <p class="mb-0">Nenhuma observação registrada para esta consulta.</p>
                                                    <a href="{{ url_for('main.add_post_appointment_notes', id=appointment.id) }}" class="alert-link">Adicionar observações</a>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                        
                                        <div class="mt-3">
                                            {% if appointment.status == 'scheduled' %}
                                                <a href="{{ url_for('main.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-outline-primary">Editar</a>
                                                <a href="{{ url_for('main.complete_appointment', id=appointment.id) }}" class="btn btn-sm btn-outline-success">Concluir</a>
                                                <a href="{{ url_for('main.cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('Tem certeza que deseja cancelar este agendamento?')">Cancelar</a>
                                            {% elif appointment.status == 'completed' and not appointment.post_appointment_notes %}
                                                <a href="{{ url_for('main.add_post_appointment_notes', id=appointment.id) }}" class="btn btn-sm btn-outline-info">Adicionar Observações</a>
                                            {% elif appointment.status == 'completed' and appointment.post_appointment_notes %}
                                                <a href="{{ url_for('main.add_post_appointment_notes', id=appointment.id) }}" class="btn btn-sm btn-outline-info">Editar Observações</a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="alert alert-info mb-0">
                        Este paciente ainda não possui consultas agendadas.
                        <a href="{{ url_for('main.add_appointment') }}" class="alert-link">Agendar uma consulta</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
