{% extends "base.html" %}

{% block title %}Home - {{ super() }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-primary text-white text-center py-5 rounded-3 mb-5">
    <div class="container px-4">
        <h1 class="display-4 fw-bold mb-3">Sistema de Gestão Terapêutica</h1>
        <div class="col-lg-8 mx-auto">
            <p class="lead mb-4">
                Uma plataforma completa para conectar profissionais e pacientes, facilitando o agendamento de consultas e o acompanhamento terapêutico.
            </p>
            <div class="d-grid gap-3 d-sm-flex justify-content-sm-center mb-3">
                {% if current_user.is_authenticated %}
                    <a href="{{ url_for('auth.dashboard') }}" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-tachometer-alt me-2"></i>Acessar Dashboard
                    </a>
                {% else %}
                    <a href="{{ url_for('auth.login') }}" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-sign-in-alt me-2"></i>Login
                    </a>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-success btn-lg px-4">
                        <i class="fas fa-user-plus me-2"></i>Registrar-se
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="container px-4 py-3">
    <div class="row g-4 py-3 row-cols-1 row-cols-md-3">
        <div class="col">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-primary bg-gradient text-white mb-3 mx-auto" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-calendar-check fa-2x"></i>
                    </div>
                    <h3 class="fs-4 fw-bold mb-3">Agendamento Simplificado</h3>
                    <p class="mb-0">
                        Agende consultas com facilidade, escolhendo horários disponíveis e recebendo confirmações automáticas.
                    </p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-success bg-gradient text-white mb-3 mx-auto" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                    <h3 class="fs-4 fw-bold mb-3">Consultas Online</h3>
                    <p class="mb-0">
                        Realize consultas remotas através de links do Google Meet gerados automaticamente para cada agendamento.
                    </p>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-info bg-gradient text-white mb-3 mx-auto" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                    <h3 class="fs-4 fw-bold mb-3">Histórico Completo</h3>
                    <p class="mb-0">
                        Mantenha um registro organizado de todas as consultas, com anotações e acompanhamento do progresso.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Types Section -->
<div class="container px-4 py-3">
    <h2 class="text-center mb-4">Para Todos os Usuários</h2>
    <div class="row g-4">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-primary bg-gradient text-white rounded-circle p-3 me-3">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <h5 class="card-title mb-0">Administradores</h5>
                    </div>
                    <p class="card-text">Gerencie profissionais, adicione novos profissionais e monitore o sistema com ferramentas administrativas completas.</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Gerenciamento de profissionais</li>
                        <li><i class="fas fa-check text-success me-2"></i>Monitoramento do sistema</li>
                        <li><i class="fas fa-check text-success me-2"></i>Controle de acesso</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success bg-gradient text-white rounded-circle p-3 me-3">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h5 class="card-title mb-0">Profissionais</h5>
                    </div>
                    <p class="card-text">Gerencie seus pacientes, agende consultas e mantenha registros organizados em uma interface intuitiva.</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Gerenciamento de pacientes</li>
                        <li><i class="fas fa-check text-success me-2"></i>Agenda personalizada</li>
                        <li><i class="fas fa-check text-success me-2"></i>Registro de consultas</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-info bg-gradient text-white rounded-circle p-3 me-3">
                            <i class="fas fa-user"></i>
                        </div>
                        <h5 class="card-title mb-0">Pacientes</h5>
                    </div>
                    <p class="card-text">Registre-se como paciente, agende consultas com profissionais e gerencie seus agendamentos com facilidade.</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Agendamento de consultas</li>
                        <li><i class="fas fa-check text-success me-2"></i>Histórico de atendimentos</li>
                        <li><i class="fas fa-check text-success me-2"></i>Consultas online</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="container px-4 py-5">
    <div class="p-5 mb-4 bg-light rounded-3 text-center">
        <div class="container-fluid py-3">
            <h2 class="display-6 fw-bold mb-3">Comece a usar hoje mesmo!</h2>
            <p class="fs-5 mb-4">Registre-se como paciente ou explore os profissionais disponíveis.</p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                {% if not current_user.is_authenticated %}
                    <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg px-4 me-sm-3">
                        <i class="fas fa-user-plus me-2"></i>Registrar-se
                    </a>
                    <a href="{{ url_for('patient.professionals') }}" class="btn btn-outline-primary btn-lg px-4">
                        <i class="fas fa-user-md me-2"></i>Ver Profissionais
                    </a>
                {% else %}
                    <a href="{{ url_for('patient.professionals') }}" class="btn btn-primary btn-lg px-4">
                        <i class="fas fa-user-md me-2"></i>Ver Profissionais
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
