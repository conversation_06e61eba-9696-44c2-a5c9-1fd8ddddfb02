#!/usr/bin/env python3
"""
Script para adicionar horários de agenda para o profissional criado
"""

import sys
import os

# Adicionar o diretório do projeto ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models import User, Professional, Schedule, init_db

def add_schedule_for_professional():
    """Adiciona horários de agenda para o Dr. Matheus <PERSON>"""
    
    # Inicializar o banco de dados
    init_db()
    
    # Buscar o profissional pelo email
    email = "<EMAIL>"
    user = User.get_by_email(email)
    
    if not user:
        print(f"❌ Usuário com email {email} não encontrado!")
        return False
    
    professional = Professional.get_by_user_id(user.id)
    if not professional:
        print(f"❌ Profissional não encontrado para o usuário {user.username}!")
        return False
    
    print(f"✅ Profissional encontrado: {professional.name}")
    
    # Definir horários de agenda (Segunda a Sexta, 8h às 18h)
    schedules = [
        # Segunda-feira (0)
        {"day": 0, "start": "08:00", "end": "12:00"},
        {"day": 0, "start": "14:00", "end": "18:00"},
        
        # Terça-feira (1)
        {"day": 1, "start": "08:00", "end": "12:00"},
        {"day": 1, "start": "14:00", "end": "18:00"},
        
        # Quarta-feira (2)
        {"day": 2, "start": "08:00", "end": "12:00"},
        {"day": 2, "start": "14:00", "end": "18:00"},
        
        # Quinta-feira (3)
        {"day": 3, "start": "08:00", "end": "12:00"},
        {"day": 3, "start": "14:00", "end": "18:00"},
        
        # Sexta-feira (4)
        {"day": 4, "start": "08:00", "end": "12:00"},
        {"day": 4, "start": "14:00", "end": "17:00"},
    ]
    
    days_names = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo']
    
    try:
        # Verificar se já existem horários
        existing_schedules = Schedule.get_by_professional_id(professional.id)
        if existing_schedules:
            print(f"⚠️ Profissional já possui {len(existing_schedules)} horários cadastrados.")
            print("📅 Horários existentes:")
            for schedule in existing_schedules:
                print(f"   {days_names[schedule.day_of_week]}: {schedule.start_time} - {schedule.end_time}")
            return True
        
        # Criar os horários
        created_count = 0
        for schedule_data in schedules:
            schedule = Schedule(
                professional_id=professional.id,
                day_of_week=schedule_data["day"],
                start_time=schedule_data["start"],
                end_time=schedule_data["end"]
            )
            schedule.save()
            created_count += 1
            print(f"✅ Horário criado: {days_names[schedule_data['day']]} {schedule_data['start']} - {schedule_data['end']}")
        
        print(f"\n🎉 {created_count} horários criados com sucesso!")
        print("\n📅 Agenda do Dr. Matheus Parreira:")
        print("   Segunda a Quinta: 08:00-12:00 e 14:00-18:00")
        print("   Sexta-feira: 08:00-12:00 e 14:00-17:00")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar horários: {e}")
        return False

if __name__ == "__main__":
    print("📅 Adicionando horários de agenda...")
    success = add_schedule_for_professional()
    
    if success:
        print("\n✅ Agenda configurada com sucesso!")
        print("\n🎯 Agora você pode:")
        print("   1. Fazer login como profissional")
        print("   2. Ver os horários na agenda")
        print("   3. Agendar consultas")
        print("   4. Testar como paciente agendando consultas")
    else:
        print("\n❌ Falha ao configurar agenda!")
