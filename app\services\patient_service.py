"""
Patient Service - Single Responsibility Principle
Responsável pela lógica de negócio de pacientes
"""

from typing import List, Optional
from datetime import date
from app.models.patient import Patient
from app.repositories.patient_repository import PatientRepository
from app.repositories.user_repository import UserRepository
from app.services.auth_service import AuthService


class PatientService:
    """
    Serviço de pacientes
    Responsabilidades:
    - Criar e gerenciar pacientes
    - Validar dados de pacientes
    - Integrar com usuários
    """
    
    def __init__(self):
        self.patient_repo = PatientRepository()
        self.user_repo = UserRepository()
        self.auth_service = AuthService()
    
    def create_patient(self, name: str, email: str = None, phone: str = None,
                      date_of_birth: date = None, address: str = None,
                      professional_id: int = None, user_id: int = None,
                      emergency_contact: str = None, emergency_phone: str = None) -> Patient:
        """
        Cria um novo paciente
        
        Args:
            name: Nome do paciente
            email: Email do paciente
            phone: Telefone do paciente
            date_of_birth: Data de nascimento
            address: Endereço
            professional_id: ID do profissional responsável
            user_id: ID do usuário (se paciente tem conta)
            emergency_contact: Contato de emergência
            emergency_phone: Telefone de emergência
            
        Returns:
            Patient object criado
            
        Raises:
            ValueError: Se dados inválidos
        """
        # Validações
        if not name or len(name.strip()) < 2:
            raise ValueError("Nome do paciente é obrigatório e deve ter pelo menos 2 caracteres")
        
        if email and self.patient_repo.get_by_email(email):
            raise ValueError("Já existe um paciente com este email")
        
        # Criar paciente
        return self.patient_repo.create(
            name=name,
            email=email,
            phone=phone,
            date_of_birth=date_of_birth,
            address=address,
            professional_id=professional_id,
            user_id=user_id,
            emergency_contact=emergency_contact,
            emergency_phone=emergency_phone,
            is_active=True
        )
    
    def create_patient_with_user(self, name: str, email: str, phone: str = None,
                               date_of_birth: date = None, address: str = None,
                               professional_id: int = None, password: str = None) -> Patient:
        """
        Cria um paciente com conta de usuário
        
        Args:
            name: Nome do paciente
            email: Email do paciente
            phone: Telefone
            date_of_birth: Data de nascimento
            address: Endereço
            professional_id: ID do profissional
            password: Senha para a conta (se não fornecida, gera automaticamente)
            
        Returns:
            Patient object criado
        """
        # Gerar username baseado no email
        username = email.split('@')[0]
        
        # Verificar se username já existe e modificar se necessário
        counter = 1
        original_username = username
        while self.user_repo.username_exists(username):
            username = f"{original_username}{counter}"
            counter += 1
        
        # Gerar senha se não fornecida
        if not password:
            import secrets
            import string
            password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
        
        # Criar usuário
        user = self.auth_service.create_user(username, email, password, 'patient')
        
        # Criar paciente
        patient = self.create_patient(
            name=name,
            email=email,
            phone=phone,
            date_of_birth=date_of_birth,
            address=address,
            professional_id=professional_id,
            user_id=user.id
        )
        
        return patient
    
    def update_patient(self, patient_id: int, **kwargs) -> bool:
        """Atualiza dados do paciente"""
        patient = self.patient_repo.get_by_id(patient_id)
        if not patient:
            return False
        
        # Validar email se fornecido
        if 'email' in kwargs and kwargs['email']:
            existing = self.patient_repo.get_by_email(kwargs['email'])
            if existing and existing.id != patient_id:
                raise ValueError("Já existe um paciente com este email")
        
        self.patient_repo.update(patient, **kwargs)
        return True
    
    def delete_patient(self, patient_id: int) -> bool:
        """Remove um paciente"""
        patient = self.patient_repo.get_by_id(patient_id)
        if not patient:
            return False
        
        # Se paciente tem usuário associado, desativar em vez de deletar
        if patient.user_id:
            patient.deactivate()
            if patient.user_id:
                self.auth_service.deactivate_user(patient.user_id)
        else:
            self.patient_repo.delete(patient)
        
        return True
    
    def get_patient_by_id(self, patient_id: int) -> Optional[Patient]:
        """Busca paciente por ID"""
        return self.patient_repo.get_by_id(patient_id)
    
    def get_patients_by_professional(self, professional_id: int) -> List[Patient]:
        """Busca pacientes por profissional"""
        return self.patient_repo.get_by_professional_id(professional_id)
    
    def get_active_patients(self) -> List[Patient]:
        """Retorna pacientes ativos"""
        return self.patient_repo.get_active_patients()
    
    def search_patients(self, search_term: str) -> List[Patient]:
        """Busca pacientes por nome"""
        return self.patient_repo.search_by_name(search_term)
    
    def activate_patient(self, patient_id: int) -> bool:
        """Ativa um paciente"""
        patient = self.patient_repo.get_by_id(patient_id)
        if not patient:
            return False
        
        patient.activate()
        if patient.user_id:
            self.auth_service.activate_user(patient.user_id)
        
        return True
    
    def deactivate_patient(self, patient_id: int) -> bool:
        """Desativa um paciente"""
        patient = self.patient_repo.get_by_id(patient_id)
        if not patient:
            return False
        
        patient.deactivate()
        if patient.user_id:
            self.auth_service.deactivate_user(patient.user_id)
        
        return True
