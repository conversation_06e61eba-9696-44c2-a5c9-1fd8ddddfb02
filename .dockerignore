# Git
.git
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
app.db
*.sqlite
*.sqlite3
node_modules/
.env.local
.env.production
token.pickle

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
