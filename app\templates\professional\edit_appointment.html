{% extends "base.html" %}

{% block title %}Edit Appointment - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Editar Agendamento</h4>
                <a href="{{ url_for('main.professional_appointments') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.patient_id.label(class="form-label") }}
                        {{ form.patient_id(class="form-select") }}
                        {% for error in form.patient_id.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.appointment_date.label(class="form-label") }}
                        {{ form.appointment_date(class="form-control", type="date") }}
                        {% for error in form.appointment_date.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Horário de Início</label>
                                <input type="text" class="form-control" value="{{ appointment.start_time }}" readonly>
                                {{ form.start_time(value=appointment.start_time) }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Horário de Término</label>
                                <input type="text" class="form-control" value="{{ appointment.end_time }}" readonly>
                                {{ form.end_time(value=appointment.end_time) }}
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows=3) }}
                        {% for error in form.notes.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="alert alert-warning mb-0">
                    <strong>Atenção:</strong> Alterar a data ou horário pode causar conflitos com outros agendamentos.
                    Certifique-se de verificar a disponibilidade antes de salvar.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
