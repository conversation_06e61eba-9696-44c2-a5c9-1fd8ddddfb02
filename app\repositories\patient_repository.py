"""
Patient Repository - Single Responsibility Principle
Responsável pelo acesso aos dados de pacientes
"""

from typing import Optional, List
from app.models.patient import Patient
from .base_repository import BaseRepository


class PatientRepository(BaseRepository):
    """Repositório para operações específicas de pacientes"""
    
    def __init__(self):
        super().__init__(Patient)
    
    def get_by_user_id(self, user_id: int) -> Optional[Patient]:
        """Busca paciente por ID do usuário"""
        return self.find_one_by(user_id=user_id)
    
    def get_by_professional_id(self, professional_id: int) -> List[Patient]:
        """Busca pacientes por profissional"""
        return self.find_by(professional_id=professional_id)
    
    def get_by_email(self, email: str) -> Optional[Patient]:
        """Busca paciente por email"""
        return self.find_one_by(email=email)
    
    def search_by_name(self, name: str) -> List[Patient]:
        """Busca pacientes por nome"""
        search_pattern = f"%{name}%"
        return Patient.query.filter(
            Patient.name.ilike(search_pattern)
        ).all()
    
    def get_active_patients(self) -> List[Patient]:
        """Retorna pacientes ativos"""
        return self.find_by(is_active=True)
    
    def get_with_upcoming_appointments(self) -> List[Patient]:
        """Retorna pacientes com agendamentos futuros"""
        from app.models.appointment import Appointment
        from datetime import date
        from app import db
        
        return Patient.query.join(Appointment).filter(
            Appointment.appointment_date >= date.today(),
            Appointment.status != 'cancelled'
        ).distinct().all()
    
    def get_repository_specific_methods(self):
        return [
            'get_by_user_id', 'get_by_professional_id', 'get_by_email',
            'search_by_name', 'get_active_patients', 'get_with_upcoming_appointments'
        ]
