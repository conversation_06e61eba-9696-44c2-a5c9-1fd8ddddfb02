"""
Auth Service - Single Responsibility Principle
Responsável pela lógica de negócio de autenticação e autorização
"""

from typing import Optional
from flask_login import login_user, logout_user
from app.models.user import User
from app.repositories.user_repository import UserRepository


class AuthService:
    """
    Serviço de autenticação
    Responsabilidades:
    - Autenticação de usuários
    - Criação de contas
    - Gerenciamento de sessões
    - Validações de negócio
    """
    
    def __init__(self):
        self.user_repository = UserRepository()
    
    def authenticate(self, username_or_email: str, password: str) -> Optional[User]:
        """
        Autentica um usuário
        
        Args:
            username_or_email: Username ou email do usuário
            password: Senha do usuário
            
        Returns:
            User object se autenticação bem-sucedida, None caso contrário
        """
        user = self.user_repository.authenticate(username_or_email, password)
        
        if user:
            # Log da autenticação bem-sucedida
            print(f"✅ User authenticated: {user.username} ({user.role})")
        
        return user
    
    def login(self, username_or_email: str, password: str, remember_me: bool = False) -> bool:
        """
        Realiza o login do usuário
        
        Args:
            username_or_email: Username ou email
            password: Senha
            remember_me: Se deve lembrar do login
            
        Returns:
            True se login bem-sucedido, False caso contrário
        """
        user = self.authenticate(username_or_email, password)
        
        if user:
            login_user(user, remember=remember_me)
            return True
        
        return False
    
    def logout(self) -> None:
        """Realiza o logout do usuário"""
        logout_user()
    
    def create_user(self, username: str, email: str, password: str, role: str = 'patient') -> User:
        """
        Cria um novo usuário
        
        Args:
            username: Nome de usuário
            email: Email do usuário
            password: Senha
            role: Role do usuário (admin, professional, patient)
            
        Returns:
            User object criado
            
        Raises:
            ValueError: Se dados inválidos ou usuário já existe
        """
        # Validações de negócio
        self._validate_user_data(username, email, password, role)
        
        # Verificar se usuário já existe
        if self.user_repository.username_exists(username):
            raise ValueError(f"Username '{username}' já está em uso")
        
        if self.user_repository.email_exists(email):
            raise ValueError(f"Email '{email}' já está em uso")
        
        # Criar usuário
        return self.user_repository.create_user(username, email, password, role)
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """
        Altera a senha do usuário
        
        Args:
            user_id: ID do usuário
            old_password: Senha atual
            new_password: Nova senha
            
        Returns:
            True se alteração bem-sucedida
            
        Raises:
            ValueError: Se senha atual incorreta ou nova senha inválida
        """
        self._validate_password(new_password)
        return self.user_repository.change_password(user_id, old_password, new_password)
    
    def reset_password(self, user_id: int, new_password: str) -> bool:
        """
        Reseta a senha do usuário (apenas admin)
        
        Args:
            user_id: ID do usuário
            new_password: Nova senha
            
        Returns:
            True se reset bem-sucedido
        """
        self._validate_password(new_password)
        return self.user_repository.reset_password(user_id, new_password)
    
    def activate_user(self, user_id: int) -> bool:
        """Ativa um usuário"""
        return self.user_repository.activate_user(user_id)
    
    def deactivate_user(self, user_id: int) -> bool:
        """Desativa um usuário"""
        return self.user_repository.deactivate_user(user_id)
    
    def change_user_role(self, user_id: int, new_role: str) -> bool:
        """
        Altera o role do usuário
        
        Args:
            user_id: ID do usuário
            new_role: Novo role
            
        Returns:
            True se alteração bem-sucedida
        """
        valid_roles = ['admin', 'professional', 'patient']
        if new_role not in valid_roles:
            raise ValueError(f"Role inválido. Deve ser um de: {valid_roles}")
        
        return self.user_repository.change_user_role(user_id, new_role)
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Busca usuário por ID"""
        return self.user_repository.get_by_id(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Busca usuário por username"""
        return self.user_repository.get_by_username(username)
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """Busca usuário por email"""
        return self.user_repository.get_by_email(email)
    
    def _validate_user_data(self, username: str, email: str, password: str, role: str) -> None:
        """Valida dados do usuário"""
        # Validar username
        if not username or len(username.strip()) < 3:
            raise ValueError("Username deve ter pelo menos 3 caracteres")
        
        if not username.replace('_', '').replace('-', '').isalnum():
            raise ValueError("Username deve conter apenas letras, números, _ e -")
        
        # Validar email
        if not email or '@' not in email or '.' not in email:
            raise ValueError("Email inválido")
        
        # Validar senha
        self._validate_password(password)
        
        # Validar role
        valid_roles = ['admin', 'professional', 'patient']
        if role not in valid_roles:
            raise ValueError(f"Role inválido. Deve ser um de: {valid_roles}")
    
    def _validate_password(self, password: str) -> None:
        """Valida senha"""
        if not password or len(password) < 6:
            raise ValueError("Senha deve ter pelo menos 6 caracteres")
        
        # Adicionar mais validações se necessário
        # Ex: deve conter maiúscula, minúscula, número, etc.
    
    def get_user_statistics(self) -> dict:
        """Retorna estatísticas dos usuários"""
        return self.user_repository.get_user_statistics()
