{% extends "base.html" %}

{% block title %}View Professionals - {{ super() }}{% endblock %}

{% block content %}
<div class="mb-4">
    <h2>Profissionais Disponíveis</h2>
    <p class="lead">Encontre um profissional e veja sua disponibilidade para agendamento.</p>
</div>

<div class="row row-cols-1 row-cols-md-3 g-4">
    {% for professional in professionals %}
        <div class="col">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ professional.name }}</h5>
                    <h6 class="card-subtitle mb-2 text-muted">{{ professional.specialty }}</h6>
                    <p class="card-text">
                        <strong>Registro:</strong> {{ professional.license_number }}<br>
                        <strong>Contato:</strong> {{ professional.phone }}
                    </p>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('main.patient_view_professional_schedule', id=professional.id) }}" class="btn btn-primary">Ver Agenda</a>
                </div>
            </div>
        </div>
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                Nenhum profissional cadastrado no momento.
            </div>
        </div>
    {% endfor %}
</div>
{% endblock %}
