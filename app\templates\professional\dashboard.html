{% extends "base.html" %}

{% block title %}Professional Dashboard - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Dashboard do Profissional</h2>
    <a href="{{ url_for('main.add_patient') }}" class="btn btn-primary">Adicionar <PERSON></a>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Meus Dad<PERSON></h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nome:</strong> {{ professional.name }}</p>
                <p><strong>Especialidade:</strong> {{ professional.specialty }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Número de Registro:</strong> {{ professional.license_number }}</p>
                <p><strong>Telefone:</strong> {{ professional.phone }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Meus Pacientes</h5>
    </div>
    <div class="card-body">
        {% if patients %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Email</th>
                            <th>Telefone</th>
                            <th>Data de Nascimento</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                            <tr>
                                <td>{{ patient.name }}</td>
                                <td>{{ patient.email }}</td>
                                <td>{{ patient.phone }}</td>
                                <td>{{ patient.date_of_birth.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('main.view_patient', id=patient.id) }}" class="btn btn-outline-info">Detalhes</a>
                                        <a href="{{ url_for('main.edit_patient', id=patient.id) }}" class="btn btn-outline-primary">Editar</a>
                                        <a href="{{ url_for('main.delete_patient', id=patient.id) }}" class="btn btn-outline-danger" onclick="return confirm('Tem certeza que deseja excluir este paciente?')">Excluir</a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                Nenhum paciente cadastrado. <a href="{{ url_for('main.add_patient') }}">Adicionar um paciente</a>.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
