{% extends "base.html" %}

{% block title %}Professional Dashboard - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Dashboard do Profissional</h2>
    <button class="btn btn-primary" onclick="alert('Funcionalidade em desenvolvimento')">Adicionar <PERSON></button>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Meus Dados</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Nome:</strong> {{ professional.name }}</p>
                <p><strong>Especialidade:</strong> {{ professional.specialty }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Número de Registro:</strong> {{ professional.license_number }}</p>
                <p><strong>Telefone:</strong> {{ professional.phone }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Meus Pacientes</h5>
    </div>
    <div class="card-body">
        {% if patients %}
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Email</th>
                            <th>Telefone</th>
                            <th>Data de Nascimento</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                            <tr>
                                <td>{{ patient.name }}</td>
                                <td>{{ patient.email }}</td>
                                <td>{{ patient.phone }}</td>
                                <td>{{ patient.date_of_birth.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="alert('Funcionalidade em desenvolvimento')">Detalhes</button>
                                        <button class="btn btn-outline-primary" onclick="alert('Funcionalidade em desenvolvimento')">Editar</button>
                                        <button class="btn btn-outline-danger" onclick="alert('Funcionalidade em desenvolvimento')">Excluir</button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                Nenhum paciente cadastrado. <button class="btn btn-link p-0" onclick="alert('Funcionalidade em desenvolvimento')">Adicionar um paciente</button>.
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
