{% extends "base.html" %}

{% block title %}Agendar Consulta - Sistema de Gestão Terapêutica{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>Agendar Nova Consulta
                </h4>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="professional_id" class="form-label">Profissional *</label>
                                <select class="form-select" id="professional_id" name="professional_id" required>
                                    <option value="">Selecione um profissional</option>
                                    {% for professional in professionals %}
                                        <option value="{{ professional.id }}"
                                                {% if selected_professional and selected_professional.id == professional.id %}selected{% endif %}>
                                            {{ professional.name }} - {{ professional.specialty }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointment_date" class="form-label">Data da Consulta *</label>
                                <input type="date" class="form-control" id="appointment_date" name="appointment_date"
                                       value="{{ request.args.get('date', '') }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_time" class="form-label">Horário de Início *</label>
                                <input type="time" class="form-control" id="start_time" name="start_time"
                                       value="{{ request.args.get('start_time', '') }}" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_time" class="form-label">Horário de Fim *</label>
                                <input type="time" class="form-control" id="end_time" name="end_time"
                                       value="{{ request.args.get('end_time', '') }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Observações</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="Descreva brevemente o motivo da consulta ou observações importantes..."></textarea>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Informações importantes:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Um link do Google Meet será gerado automaticamente para a consulta</li>
                            <li>Você receberá o link por email e poderá acessá-lo no dia da consulta</li>
                            <li>Certifique-se de que o horário selecionado está disponível</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('patient.professionals') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Voltar
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-2"></i>Confirmar Agendamento
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {% if selected_professional %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-md me-2"></i>Informações do Profissional
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Nome:</strong> {{ selected_professional.name }}</p>
                        <p><strong>Especialidade:</strong> {{ selected_professional.specialty }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Registro:</strong> {{ selected_professional.license_number }}</p>
                        <p><strong>Telefone:</strong> {{ selected_professional.phone }}</p>
                    </div>
                </div>
                {% if selected_professional.bio %}
                <div class="mt-2">
                    <p><strong>Sobre:</strong></p>
                    <p class="text-muted">{{ selected_professional.bio }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Definir data mínima como hoje
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('appointment_date').min = today;

    // Auto-calcular horário de fim baseado no início (1 hora de duração padrão)
    document.getElementById('start_time').addEventListener('change', function() {
        const startTime = this.value;
        if (startTime) {
            const [hours, minutes] = startTime.split(':');
            const endDate = new Date();
            endDate.setHours(parseInt(hours) + 1, parseInt(minutes));

            const endTime = endDate.toTimeString().slice(0, 5);
            document.getElementById('end_time').value = endTime;
        }
    });

    // Validar que horário de fim é posterior ao início
    function validateTimes() {
        const startTime = document.getElementById('start_time').value;
        const endTime = document.getElementById('end_time').value;

        if (startTime && endTime && startTime >= endTime) {
            alert('O horário de fim deve ser posterior ao horário de início.');
            return false;
        }
        return true;
    }

    document.getElementById('end_time').addEventListener('change', validateTimes);

    // Validar formulário antes do envio
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!validateTimes()) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
