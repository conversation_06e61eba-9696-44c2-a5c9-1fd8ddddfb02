{% extends "base.html" %}

{% block title %}Registro de Paciente - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Registro de Paciente</h4>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.username.label(class="form-label") }}
                                {{ form.username(class="form-control") }}
                                {% for error in form.username.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                                <small class="form-text text-muted">Este será seu nome de usuário para login.</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.email.label(class="form-label") }}
                                {{ form.email(class="form-control") }}
                                {% for error in form.email.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password.label(class="form-label") }}
                                {{ form.password(class="form-control") }}
                                {% for error in form.password.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                                <small class="form-text text-muted">Mínimo de 6 caracteres.</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.password2.label(class="form-label") }}
                                {{ form.password2(class="form-control") }}
                                {% for error in form.password2.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    <h5>Informações Pessoais</h5>
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% for error in form.name.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.phone.label(class="form-label") }}
                                {{ form.phone(class="form-control") }}
                                {% for error in form.phone.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.date_of_birth.label(class="form-label") }}
                                {{ form.date_of_birth(class="form-control", type="date") }}
                                {% for error in form.date_of_birth.errors %}
                                    <div class="text-danger">{{ error }}</div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control") }}
                        {% for error in form.address.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid mt-4">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Já tem uma conta? <a href="{{ url_for('main.login') }}">Faça login</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
