{% extends "base.html" %}

{% block title %}Login - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-5">
        <div class="card">
            <div class="card-header bg-gradient-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-circle fa-4x text-primary mb-3"></i>
                    <p class="text-muted">Entre com suas credenciais para acessar o sistema</p>
                </div>

                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="fas fa-user text-primary"></i></span>
                            {{ form.username(class="form-control", placeholder="Nome de usuário") }}
                        </div>
                        {% for error in form.username.errors %}
                            <div class="text-danger small mt-1"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="fas fa-lock text-primary"></i></span>
                            {{ form.password(class="form-control", placeholder="Senha") }}
                        </div>
                        {% for error in form.password.errors %}
                            <div class="text-danger small mt-1"><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary py-2") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Não tem uma conta?</p>
                <div class="mt-2">
                    <a href="{{ url_for('main.patient_register') }}" class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-user me-1"></i> Registrar como Paciente
                    </a>
                    <a href="{{ url_for('main.register') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-user-tie me-1"></i> Registrar como Profissional
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
