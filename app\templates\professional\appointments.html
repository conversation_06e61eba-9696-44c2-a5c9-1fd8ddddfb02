{% extends "base.html" %}

{% block title %}My Appointments - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Meus Agendamentos</h2>
    <div>
        <a href="{{ url_for('main.professional_consultations') }}" class="btn btn-outline-info me-2">Ver Consultas Realizadas</a>
        <a href="{{ url_for('main.add_appointment') }}" class="btn btn-primary">Agendar Consulta</a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Filtrar Agendamentos</h5>
    </div>
    <div class="card-body">
        <form method="post" class="row g-3">
            {{ filter_form.hidden_tag() }}

            <div class="col-md-3">
                {{ filter_form.start_date.label(class="form-label") }}
                {{ filter_form.start_date(class="form-control", type="date") }}
            </div>

            <div class="col-md-3">
                {{ filter_form.end_date.label(class="form-label") }}
                {{ filter_form.end_date(class="form-control", type="date") }}
            </div>

            <div class="col-md-3">
                {{ filter_form.patient_id.label(class="form-label") }}
                {{ filter_form.patient_id(class="form-select") }}
            </div>

            <div class="col-md-3">
                {{ filter_form.status.label(class="form-label") }}
                {{ filter_form.status(class="form-select") }}
            </div>

            <div class="col-12">
                {{ filter_form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Resultados</h5>
        {% if appointments_by_date %}
            {% set total_appointments = namespace(count=0) %}
            {% for date_str, appointments in appointments_by_date.items() %}
                {% set total_appointments.count = total_appointments.count + appointments|length %}
            {% endfor %}
            <span class="badge bg-primary">{{ total_appointments.count }} agendamento(s) encontrado(s)</span>
        {% else %}
            <span class="badge bg-secondary">Nenhum agendamento encontrado</span>
        {% endif %}
    </div>
    <div class="card-body">
        {% if appointments_by_date %}
            <div class="accordion" id="appointmentsAccordion">
                {% for date_str, appointments in appointments_by_date.items() %}
                    {% set date_obj = date_str|string %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ loop.index }}">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ loop.index }}" aria-expanded="true" aria-controls="collapse{{ loop.index }}">
                                {{ date_str }}
                                <span class="badge bg-primary ms-2">{{ appointments|length }}</span>
                            </button>
                        </h2>
                        <div id="collapse{{ loop.index }}" class="accordion-collapse collapse show" aria-labelledby="heading{{ loop.index }}" data-bs-parent="#appointmentsAccordion">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Horário</th>
                                                <th>Paciente</th>
                                                <th>Status</th>
                                                <th>Notas</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for appointment in appointments %}
                                                {% set patient = appointment.get_patient() %}
                                                <tr>
                                                    <td>{{ appointment.start_time }} - {{ appointment.end_time }}</td>
                                                    <td>{{ patient.name }}</td>
                                                    <td>
                                                        {% if appointment.status == 'scheduled' %}
                                                            <span class="badge bg-primary">Agendado</span>
                                                        {% elif appointment.status == 'completed' %}
                                                            <span class="badge bg-success">Concluído</span>
                                                            {% if appointment.post_appointment_notes %}
                                                                <span class="badge bg-info ms-1" title="Possui observações">
                                                                    <i class="bi bi-journal-text"></i>
                                                                </span>
                                                            {% endif %}
                                                        {% elif appointment.status == 'cancelled' %}
                                                            <span class="badge bg-danger">Cancelado</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ appointment.notes|truncate(30) }}</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            {% if appointment.status == 'scheduled' %}
                                                                {% if appointment.meeting_link %}
                                                                    <a href="{{ appointment.meeting_link }}" target="_blank" class="btn btn-outline-primary">Entrar na Reunião</a>
                                                                {% endif %}
                                                                <a href="{{ url_for('main.edit_appointment', id=appointment.id) }}" class="btn btn-outline-primary">Editar</a>
                                                                <a href="{{ url_for('main.complete_appointment', id=appointment.id) }}" class="btn btn-outline-success">Concluir</a>
                                                                <a href="{{ url_for('main.cancel_appointment', id=appointment.id) }}" class="btn btn-outline-danger" onclick="return confirm('Tem certeza que deseja cancelar este agendamento?')">Cancelar</a>
                                                            {% elif appointment.status == 'completed' %}
                                                                {% if appointment.meeting_link %}
                                                                    <a href="{{ appointment.meeting_link }}" target="_blank" class="btn btn-outline-primary">Entrar na Reunião</a>
                                                                {% endif %}
                                                                <a href="{{ url_for('main.add_post_appointment_notes', id=appointment.id) }}" class="btn btn-outline-info">
                                                                    {% if appointment.post_appointment_notes %}Editar Observações{% else %}Adicionar Observações{% endif %}
                                                                </a>
                                                            {% else %}
                                                                <button class="btn btn-outline-secondary" disabled>Finalizado</button>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                Nenhum agendamento encontrado com os filtros selecionados.
                <a href="{{ url_for('main.add_appointment') }}" class="alert-link">Agendar uma consulta</a> ou ajuste os filtros para ver mais resultados.
            </div>
        {% endif %}
    </div>
</div>

<div class="alert alert-info">
    <h5>Dicas:</h5>
    <ul>
        <li>Você pode agendar consultas para seus pacientes.</li>
        <li>Marque as consultas como "Concluídas" após o atendimento.</li>
        <li>Cancele agendamentos quando necessário.</li>
    </ul>
</div>
{% endblock %}
