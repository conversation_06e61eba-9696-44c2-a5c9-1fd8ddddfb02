{% extends "base.html" %}

{% block title %}Meus Pacientes - Sistema de Gestão Terapêutica{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-users me-2"></i>Meus Pacientes</h2>
    <button class="btn btn-primary" onclick="alert('Funcionalidade em desenvolvimento')">
        <i class="fas fa-user-plus me-2"></i>Adicionar <PERSON>e
    </button>
</div>

{% if patients %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Lista de Pacientes ({{ patients|length }})</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Email</th>
                        <th>Telefone</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in patients %}
                    <tr>
                        <td>
                            <i class="fas fa-user me-2"></i>
                            {{ patient.name }}
                        </td>
                        <td>{{ patient.email or '-' }}</td>
                        <td>{{ patient.phone or '-' }}</td>
                        <td>
                            {% if patient.is_active %}
                                <span class="badge bg-success">Ativo</span>
                            {% else %}
                                <span class="badge bg-secondary">Inativo</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="alert('Funcionalidade em desenvolvimento')">
                                    <i class="fas fa-eye"></i> Ver
                                </button>
                                <button class="btn btn-outline-primary" onclick="alert('Funcionalidade em desenvolvimento')">
                                    <i class="fas fa-edit"></i> Editar
                                </button>
                                <button class="btn btn-outline-success" onclick="alert('Funcionalidade em desenvolvimento')">
                                    <i class="fas fa-calendar-plus"></i> Agendar
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center">
        <i class="fas fa-users fa-4x text-muted mb-3"></i>
        <h4>Nenhum paciente cadastrado</h4>
        <p class="text-muted">Você ainda não possui pacientes cadastrados em seu perfil.</p>
        <button class="btn btn-primary" onclick="alert('Funcionalidade em desenvolvimento')">
            <i class="fas fa-user-plus me-2"></i>Adicionar Primeiro Paciente
        </button>
    </div>
</div>
{% endif %}
{% endblock %}
