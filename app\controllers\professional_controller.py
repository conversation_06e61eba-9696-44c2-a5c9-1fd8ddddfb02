"""
Professional Controller - Single Responsibility Principle
Responsável pelas rotas específicas de profissionais
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from app.repositories.professional_repository import ProfessionalRepository
from app.repositories.patient_repository import PatientRepository
from app.repositories.appointment_repository import AppointmentRepository

# Create blueprint
professional_bp = Blueprint('professional', __name__, url_prefix='/professional')

# Initialize repositories
prof_repo = ProfessionalRepository()
patient_repo = PatientRepository()
appointment_repo = AppointmentRepository()


@professional_bp.route('/dashboard')
@login_required
def dashboard():
    """Dashboard do profissional"""
    if not current_user.is_professional():
        flash('Acesso negado. Apenas profissionais.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    # Buscar perfil do profissional
    professional = prof_repo.get_by_user_id(current_user.id)
    if not professional:
        flash('Perfil profissional não encontrado.', 'error')
        return redirect(url_for('auth.logout'))
    
    # Buscar estatísticas
    patients = patient_repo.get_by_professional_id(professional.id)
    upcoming_appointments = appointment_repo.get_upcoming_appointments(professional.id)
    today_appointments = appointment_repo.get_today_appointments(professional.id)
    
    stats = {
        'total_patients': len(patients),
        'upcoming_appointments': len(upcoming_appointments),
        'today_appointments': len(today_appointments)
    }
    
    return render_template('professional/dashboard.html', 
                         professional=professional, 
                         stats=stats,
                         upcoming_appointments=upcoming_appointments[:5],  # Próximos 5
                         today_appointments=today_appointments)


@professional_bp.route('/patients')
@login_required
def patients():
    """Lista de pacientes do profissional"""
    if not current_user.is_professional():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    professional = prof_repo.get_by_user_id(current_user.id)
    if not professional:
        flash('Perfil profissional não encontrado.', 'error')
        return redirect(url_for('auth.logout'))
    
    patients = patient_repo.get_by_professional_id(professional.id)
    
    return render_template('professional/patients.html', 
                         professional=professional,
                         patients=patients)


@professional_bp.route('/appointments')
@login_required
def appointments():
    """Lista de agendamentos do profissional"""
    if not current_user.is_professional():
        flash('Acesso negado.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    professional = prof_repo.get_by_user_id(current_user.id)
    if not professional:
        flash('Perfil profissional não encontrado.', 'error')
        return redirect(url_for('auth.logout'))
    
    # Filtros opcionais
    status = request.args.get('status')
    
    if status:
        appointments = appointment_repo.get_by_status(status, professional.id)
    else:
        appointments = appointment_repo.get_by_professional_id(professional.id)
    
    return render_template('professional/appointments.html',
                         professional=professional,
                         appointments=appointments,
                         current_status=status)
