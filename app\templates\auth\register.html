{% extends "base.html" %}

{% block title %}Registrar - Sistema de Gestão Terapêutica{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card">
                <div class="card-header text-center">
                    <h4><i class="fas fa-user-plus me-2"></i><PERSON><PERSON><PERSON></h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">Nome de Usuário</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">Mínimo 3 caracteres, apenas letras, números, _ e -</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">Mínimo 6 caracteres</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Senha</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Tipo de Usuário</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="patient" selected>Paciente</option>
                                <option value="professional">Profissional</option>
                            </select>
                            <div class="form-text">Administradores são criados apenas por outros administradores</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-user-plus me-2"></i>Criar Conta
                        </button>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Já tem conta? 
                        <a href="{{ url_for('auth.login') }}">Faça login aqui</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Validação de senha em tempo real
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Senhas não coincidem');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
