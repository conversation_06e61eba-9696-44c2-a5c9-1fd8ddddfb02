"""
Appointment Model - Single Responsibility Principle
Entidade de domínio responsável pela representação de agendamentos
"""

from datetime import datetime, date, time
from enum import Enum
from app import db
from .base_model import BaseModel


class AppointmentStatus(Enum):
    """Enum para status de agendamentos"""
    SCHEDULED = 'scheduled'
    CONFIRMED = 'confirmed'
    IN_PROGRESS = 'in_progress'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'
    NO_SHOW = 'no_show'


class Appointment(BaseModel):
    """
    Entidade Appointment - representa um agendamento
    Responsabilidades:
    - Gerenciar dados do agendamento
    - Controlar status e transições
    - Integração com serviços externos (Google Meet)
    """
    __tablename__ = 'appointments'
    
    professional_id = db.Column(db.Integer, db.ForeignKey('professionals.id'), nullable=False)
    patient_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>ey('patients.id'), nullable=False)
    appointment_date = db.Column(db.Date, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    status = db.Column(db.String(20), nullable=False, default=AppointmentStatus.SCHEDULED.value)
    notes = db.Column(db.Text)
    meeting_link = db.Column(db.String(255))
    meeting_id = db.Column(db.String(100))
    post_appointment_notes = db.Column(db.Text)
    reminder_sent = db.Column(db.Boolean, default=False)
    
    # Constraints
    __table_args__ = (
        db.CheckConstraint(
            f"status IN ('{AppointmentStatus.SCHEDULED.value}', '{AppointmentStatus.CONFIRMED.value}', "
            f"'{AppointmentStatus.IN_PROGRESS.value}', '{AppointmentStatus.COMPLETED.value}', "
            f"'{AppointmentStatus.CANCELLED.value}', '{AppointmentStatus.NO_SHOW.value}')",
            name='valid_status'
        ),
    )
    
    def __init__(self, **kwargs):
        """Inicializa o agendamento com validações"""
        super().__init__(**kwargs)
        self._validate_appointment()
    
    def _validate_appointment(self):
        """Valida os dados do agendamento"""
        if self.start_time >= self.end_time:
            raise ValueError("Start time must be before end time")
        
        if self.appointment_date < date.today():
            raise ValueError("Cannot schedule appointments in the past")
        
        # Validar se o horário está no futuro (para agendamentos de hoje)
        if self.appointment_date == date.today():
            current_time = datetime.now().time()
            if self.start_time <= current_time:
                raise ValueError("Cannot schedule appointments in the past")
    
    def get_status_display(self) -> str:
        """Retorna o status em formato legível"""
        status_map = {
            AppointmentStatus.SCHEDULED.value: 'Agendado',
            AppointmentStatus.CONFIRMED.value: 'Confirmado',
            AppointmentStatus.IN_PROGRESS.value: 'Em Andamento',
            AppointmentStatus.COMPLETED.value: 'Concluído',
            AppointmentStatus.CANCELLED.value: 'Cancelado',
            AppointmentStatus.NO_SHOW.value: 'Faltou'
        }
        return status_map.get(self.status, self.status)
    
    def get_duration_minutes(self) -> int:
        """Retorna a duração do agendamento em minutos"""
        start_datetime = datetime.combine(date.today(), self.start_time)
        end_datetime = datetime.combine(date.today(), self.end_time)
        duration = end_datetime - start_datetime
        return int(duration.total_seconds() / 60)
    
    def is_upcoming(self) -> bool:
        """Verifica se o agendamento é futuro"""
        appointment_datetime = datetime.combine(self.appointment_date, self.start_time)
        return appointment_datetime > datetime.now()
    
    def is_today(self) -> bool:
        """Verifica se o agendamento é hoje"""
        return self.appointment_date == date.today()
    
    def is_past(self) -> bool:
        """Verifica se o agendamento já passou"""
        appointment_datetime = datetime.combine(self.appointment_date, self.end_time)
        return appointment_datetime < datetime.now()
    
    def can_be_cancelled(self) -> bool:
        """Verifica se o agendamento pode ser cancelado"""
        return (self.status in [AppointmentStatus.SCHEDULED.value, AppointmentStatus.CONFIRMED.value] 
                and self.is_upcoming())
    
    def can_be_completed(self) -> bool:
        """Verifica se o agendamento pode ser marcado como concluído"""
        return self.status in [AppointmentStatus.SCHEDULED.value, AppointmentStatus.CONFIRMED.value, 
                              AppointmentStatus.IN_PROGRESS.value]
    
    def can_be_started(self) -> bool:
        """Verifica se o agendamento pode ser iniciado"""
        return (self.status in [AppointmentStatus.SCHEDULED.value, AppointmentStatus.CONFIRMED.value] 
                and self.is_today())
    
    def schedule(self):
        """Agenda o compromisso"""
        self.status = AppointmentStatus.SCHEDULED.value
        self.save()
    
    def confirm(self):
        """Confirma o agendamento"""
        if self.status != AppointmentStatus.SCHEDULED.value:
            raise ValueError("Only scheduled appointments can be confirmed")
        
        self.status = AppointmentStatus.CONFIRMED.value
        self.save()
    
    def start(self):
        """Inicia o agendamento"""
        if not self.can_be_started():
            raise ValueError("Appointment cannot be started")
        
        self.status = AppointmentStatus.IN_PROGRESS.value
        self.save()
    
    def complete(self, post_notes: str = None):
        """Completa o agendamento"""
        if not self.can_be_completed():
            raise ValueError("Appointment cannot be completed")
        
        self.status = AppointmentStatus.COMPLETED.value
        if post_notes:
            self.post_appointment_notes = post_notes
        self.save()
    
    def cancel(self, reason: str = None):
        """Cancela o agendamento"""
        if not self.can_be_cancelled():
            raise ValueError("Appointment cannot be cancelled")
        
        self.status = AppointmentStatus.CANCELLED.value
        if reason:
            if self.notes:
                self.notes += f"\n\nCancelamento: {reason}"
            else:
                self.notes = f"Cancelamento: {reason}"
        self.save()
    
    def mark_no_show(self):
        """Marca como faltou"""
        if self.status not in [AppointmentStatus.SCHEDULED.value, AppointmentStatus.CONFIRMED.value]:
            raise ValueError("Only scheduled or confirmed appointments can be marked as no-show")
        
        self.status = AppointmentStatus.NO_SHOW.value
        self.save()
    
    def reschedule(self, new_date: date, new_start_time: time, new_end_time: time):
        """Reagenda o compromisso"""
        if not self.can_be_cancelled():
            raise ValueError("Appointment cannot be rescheduled")
        
        self.appointment_date = new_date
        self.start_time = new_start_time
        self.end_time = new_end_time
        self.status = AppointmentStatus.SCHEDULED.value
        self._validate_appointment()
        self.save()
    
    def set_meeting_link(self, meeting_link: str, meeting_id: str = None):
        """Define o link da reunião"""
        self.meeting_link = meeting_link
        if meeting_id:
            self.meeting_id = meeting_id
        self.save()
    
    def add_notes(self, notes: str):
        """Adiciona notas ao agendamento"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M')
        if self.notes:
            self.notes += f"\n\n[{timestamp}] {notes}"
        else:
            self.notes = f"[{timestamp}] {notes}"
        self.save()
    
    def mark_reminder_sent(self):
        """Marca que o lembrete foi enviado"""
        self.reminder_sent = True
        self.save()
    
    def get_formatted_datetime(self) -> str:
        """Retorna data e hora formatadas"""
        return f"{self.appointment_date.strftime('%d/%m/%Y')} às {self.start_time.strftime('%H:%M')}"
    
    def get_patient_name(self) -> str:
        """Retorna o nome do paciente"""
        return self.patient.name if self.patient else "Paciente não encontrado"
    
    def get_professional_name(self) -> str:
        """Retorna o nome do profissional"""
        return self.professional.name if self.professional else "Profissional não encontrado"
    
    @classmethod
    def get_conflicts(cls, professional_id: int, appointment_date: date, 
                     start_time: time, end_time: time, exclude_id: int = None):
        """Verifica conflitos de horário"""
        query = cls.query.filter_by(
            professional_id=professional_id,
            appointment_date=appointment_date
        ).filter(cls.status != AppointmentStatus.CANCELLED.value)
        
        if exclude_id:
            query = query.filter(cls.id != exclude_id)
        
        # Verificar sobreposição de horários
        conflicts = query.filter(
            db.or_(
                db.and_(cls.start_time <= start_time, cls.end_time > start_time),
                db.and_(cls.start_time < end_time, cls.end_time >= end_time),
                db.and_(cls.start_time >= start_time, cls.end_time <= end_time)
            )
        ).all()
        
        return conflicts
    
    def to_dict(self):
        """Converte para dicionário incluindo informações úteis"""
        data = super().to_dict()
        data['status_display'] = self.get_status_display()
        data['duration_minutes'] = self.get_duration_minutes()
        data['is_upcoming'] = self.is_upcoming()
        data['is_today'] = self.is_today()
        data['is_past'] = self.is_past()
        data['formatted_datetime'] = self.get_formatted_datetime()
        data['patient_name'] = self.get_patient_name()
        data['professional_name'] = self.get_professional_name()
        return data
    
    def __repr__(self):
        return f'<Appointment {self.appointment_date} {self.start_time} - {self.get_patient_name()}>'
