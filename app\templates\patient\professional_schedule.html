{% extends "base.html" %}

{% block title %}{{ professional.name }} Schedule - {{ super() }}{% endblock %}

{% block styles %}
<style>
    .time-slot {
        cursor: pointer;
        transition: all 0.3s;
    }
    .time-slot:hover {
        background-color: #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="mb-4">
    <h2>Agenda de {{ professional.name }}</h2>
    <p class="lead">{{ professional.specialty }} - Registro: {{ professional.license_number }}</p>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Hor<PERSON><PERSON>s de Atendimento</h5>
            </div>
            <div class="card-body">
                {% set days = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sex<PERSON><PERSON>feira', 'Sábado', 'Domingo'] %}

                <div class="accordion" id="scheduleAccordion">
                    {% for day_index, schedules in schedule_by_day.items() %}
                        {% if schedules %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ day_index }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ day_index }}" aria-expanded="false" aria-controls="collapse{{ day_index }}">
                                        {{ days[day_index] }}
                                    </button>
                                </h2>
                                <div id="collapse{{ day_index }}" class="accordion-collapse collapse" aria-labelledby="heading{{ day_index }}" data-bs-parent="#scheduleAccordion">
                                    <div class="accordion-body">
                                        <ul class="list-group">
                                            {% for schedule in schedules %}
                                                <li class="list-group-item">
                                                    {{ schedule.start_time }} - {{ schedule.end_time }}
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            Este profissional ainda não definiu horários de atendimento.
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Verificar Disponibilidade</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Selecionar Data</label>
                    <input type="date" class="form-control" id="appointment-date" min="{{ today_date or '' }}">
                </div>
                <div class="d-grid">
                    <button class="btn btn-primary" onclick="loadAvailableSlots()">
                        <i class="fas fa-search me-2"></i>Buscar Horários Disponíveis
                    </button>
                </div>

                <div class="alert alert-info mt-3">
                    <p><strong>Instruções:</strong> Selecione uma data e clique em "Buscar Horários Disponíveis" para ver os horários disponíveis para agendamento.</p>
                    <p><small>Nota: Apenas datas futuras e horários futuros estão disponíveis para agendamento.</small></p>
                </div>

                <!-- Área onde os slots disponíveis serão carregados -->
                <div id="available-slots-container" class="mt-3" style="display: none;">
                    <hr>
                    <h5>Horários Disponíveis</h5>
                    <div id="slots-list"></div>
                </div>

                <div class="mt-3">
                    <a href="{{ url_for('patient.book_appointment', professional_id=professional.id) }}" class="btn btn-success w-100">
                        <i class="fas fa-calendar-plus me-2"></i>Agendar Consulta Diretamente
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function loadAvailableSlots() {
    const dateInput = document.getElementById('appointment-date');
    const selectedDate = dateInput.value;

    if (!selectedDate) {
        alert('Por favor, selecione uma data.');
        return;
    }

    const professionalId = {{ professional.id }};
    const url = `/patient/professional/${professionalId}/available-slots?date=${selectedDate}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('available-slots-container');
            const slotsList = document.getElementById('slots-list');

            if (data.slots && data.slots.length > 0) {
                const availableSlots = data.slots.filter(slot => slot.available);

                if (availableSlots.length > 0) {
                    let slotsHtml = '<div class="list-group">';
                    availableSlots.forEach(slot => {
                        slotsHtml += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${slot.start_time} - ${slot.end_time}</strong>
                                    <small class="text-muted d-block">Duração: 1 hora</small>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="bookSlot('${selectedDate}', '${slot.start_time}', '${slot.end_time}')">
                                    <i class="fas fa-calendar-plus"></i> Agendar
                                </button>
                            </div>
                        `;
                    });
                    slotsHtml += '</div>';

                    slotsHtml += `
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>${availableSlots.length} horário(s) disponível(is)</strong> para esta data.
                            Clique em "Agendar" para reservar seu horário.
                        </div>
                    `;

                    slotsList.innerHTML = slotsHtml;
                } else {
                    slotsList.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Nenhum horário disponível</strong> para esta data.
                            <br><small>Todos os horários já estão ocupados ou o profissional não atende neste dia.</small>
                        </div>
                    `;
                }
                container.style.display = 'block';
            } else {
                slotsList.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-calendar-times me-2"></i>
                        <strong>Nenhum horário configurado</strong> para esta data.
                        <br><small>O profissional não possui agenda configurada para este dia da semana.</small>
                    </div>
                `;
                container.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Erro ao carregar slots:', error);
            alert('Erro ao carregar horários disponíveis.');
        });
}

function bookSlot(date, startTime, endTime) {
    const url = `{{ url_for('patient.book_appointment') }}?professional_id={{ professional.id }}&date=${date}&start_time=${startTime}&end_time=${endTime}`;
    window.location.href = url;
}

// Definir data mínima como hoje
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('appointment-date').min = today;
});
</script>
{% endblock %}