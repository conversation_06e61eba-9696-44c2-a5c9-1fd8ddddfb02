{% extends "base.html" %}

{% block title %}{{ professional.name }} Schedule - {{ super() }}{% endblock %}

{% block styles %}
<style>
    .time-slot {
        cursor: pointer;
        transition: all 0.3s;
    }
    .time-slot:hover {
        background-color: #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="mb-4">
    <h2>Agenda de {{ professional.name }}</h2>
    <p class="lead">{{ professional.specialty }} - Registro: {{ professional.license_number }}</p>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Hor<PERSON><PERSON>s de Atendimento</h5>
            </div>
            <div class="card-body">
                {% set days = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sex<PERSON><PERSON>feira', 'Sábado', 'Domingo'] %}

                <div class="accordion" id="scheduleAccordion">
                    {% for day_index, schedules in schedule_by_day.items() %}
                        {% if schedules %}
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ day_index }}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ day_index }}" aria-expanded="false" aria-controls="collapse{{ day_index }}">
                                        {{ days[day_index] }}
                                    </button>
                                </h2>
                                <div id="collapse{{ day_index }}" class="accordion-collapse collapse" aria-labelledby="heading{{ day_index }}" data-bs-parent="#scheduleAccordion">
                                    <div class="accordion-body">
                                        <ul class="list-group">
                                            {% for schedule in schedules %}
                                                <li class="list-group-item">
                                                    {{ schedule.start_time }} - {{ schedule.end_time }}
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            Este profissional ainda não definiu horários de atendimento.
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Verificar Disponibilidade</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('main.patient_view_professional_schedule', id=professional.id) }}">
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.date.label(class="form-label") }}
                        {{ form.date(class="form-control", type="date", min=today_date) }}
                        {% for error in form.date.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>

                <div class="alert alert-info mt-3">
                    <p><strong>Instruções:</strong> Selecione uma data e clique em "Buscar Horários Disponíveis" para ver os horários disponíveis para agendamento.</p>
                    <p><small>Nota: Apenas datas futuras e horários futuros estão disponíveis para agendamento.</small></p>
                </div>

                {% if selected_date %}
                    <hr>
                    <h5>Horários Disponíveis para {{ selected_date }}</h5>

                    {% if available_slots %}
                        <div class="list-group mt-3">
                            {% for slot in available_slots %}
                                {% if current_user.is_authenticated and current_user.is_patient is defined and current_user.is_patient() %}
                                    <a href="{{ url_for('main.book_appointment', professional_id=professional.id, date=selected_date, start_time=slot.start_time, end_time=slot.end_time) }}" class="list-group-item time-slot">
                                        {{ slot.formatted }}
                                        <span class="badge bg-success float-end">Agendar</span>
                                    </a>
                                {% else %}
                                    <div class="list-group-item time-slot">
                                        {{ slot.formatted }}
                                        <span class="badge bg-success float-end">Disponível</span>
                                    </div>
                                {% endif %}
                            {% endfor %}
                        </div>

                        <div class="alert alert-info mt-3">
                            {% if current_user.is_authenticated and current_user.is_patient is defined and current_user.is_patient() %}
                                <p><strong>Nota:</strong> Clique em um horário disponível para agendar sua consulta.</p>
                            {% else %}
                                <p><strong>Nota:</strong> Para agendar uma consulta, faça <a href="{{ url_for('main.login') }}">login</a> ou <a href="{{ url_for('main.patient_register') }}">registre-se</a> como paciente.</p>
                            {% endif %}
                            <p>Horários disponíveis: {{ available_slots|length }}</p>
                        </div>
                    {% else %}
                        <div class="alert alert-warning mt-3">
                            Não há horários disponíveis para esta data. Por favor, selecione outra data.
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
