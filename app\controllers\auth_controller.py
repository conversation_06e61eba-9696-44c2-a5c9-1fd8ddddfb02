"""
Auth Controller - Single Responsibility Principle
Responsável pelas rotas de autenticação
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
from flask_login import login_required, logout_user, current_user
from app.services.auth_service import AuthService

# Create blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Initialize service
auth_service = AuthService()


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if current_user.is_authenticated:
        return redirect(url_for('auth.dashboard'))
    
    if request.method == 'POST':
        username_or_email = request.form.get('username')
        password = request.form.get('password')
        remember_me = bool(request.form.get('remember_me'))
        
        try:
            if auth_service.login(username_or_email, password, remember_me):
                flash('Login realizado com sucesso!', 'success')
                
                # Redirect based on user role
                if current_user.is_admin():
                    return redirect(url_for('auth.admin_dashboard'))
                elif current_user.is_professional():
                    return redirect(url_for('professional.dashboard'))
                else:
                    return redirect(url_for('patient.dashboard'))
            else:
                flash('Credenciais inválidas. Tente novamente.', 'error')
        
        except Exception as e:
            flash(f'Erro no login: {str(e)}', 'error')
    
    return render_template('auth/login.html')


@auth_bp.route('/logout')
@login_required
def logout():
    """Logout do usuário"""
    auth_service.logout()
    flash('Logout realizado com sucesso!', 'info')
    return redirect(url_for('auth.login'))


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Registro de novos usuários"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        role = request.form.get('role', 'patient')
        
        try:
            # Validar confirmação de senha
            if password != confirm_password:
                flash('Senhas não coincidem.', 'error')
                return render_template('auth/register.html')
            
            # Criar usuário
            user = auth_service.create_user(username, email, password, role)
            flash('Usuário criado com sucesso! Faça login para continuar.', 'success')
            return redirect(url_for('auth.login'))
        
        except ValueError as e:
            flash(str(e), 'error')
        except Exception as e:
            flash(f'Erro ao criar usuário: {str(e)}', 'error')
    
    return render_template('auth/register.html')


@auth_bp.route('/dashboard')
@login_required
def dashboard():
    """Dashboard principal baseado no role do usuário"""
    if current_user.is_admin():
        return redirect(url_for('auth.admin_dashboard'))
    elif current_user.is_professional():
        return redirect(url_for('professional.dashboard'))
    else:
        return redirect(url_for('patient.dashboard'))


@auth_bp.route('/admin/dashboard')
@login_required
def admin_dashboard():
    """Dashboard do administrador"""
    if not current_user.is_admin():
        flash('Acesso negado. Apenas administradores.', 'error')
        return redirect(url_for('auth.dashboard'))
    
    # Buscar estatísticas
    stats = auth_service.get_user_statistics()
    
    return render_template('admin/dashboard.html', stats=stats)


@auth_bp.route('/')
def index():
    """Página inicial"""
    if current_user.is_authenticated:
        return redirect(url_for('auth.dashboard'))
    return render_template('index.html')
