{% extends "base.html" %}

{% block title %}Add Schedule - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Adicionar <PERSON> na Agenda</h4>
                <a href="{{ url_for('main.professional_schedule') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.day_of_week.label(class="form-label") }}
                        {{ form.day_of_week(class="form-select") }}
                        {% for error in form.day_of_week.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.start_time.label(class="form-label") }}
                        {{ form.start_time(class="form-control", placeholder="Ex: 09:00") }}
                        {% for error in form.start_time.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <small class="form-text text-muted">Formato: HH:MM (24 horas)</small>
                    </div>
                    <div class="mb-3">
                        {{ form.end_time.label(class="form-label") }}
                        {{ form.end_time(class="form-control", placeholder="Ex: 17:00") }}
                        {% for error in form.end_time.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <small class="form-text text-muted">Formato: HH:MM (24 horas)</small>
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="alert alert-info mb-0">
                    <h5>Dicas:</h5>
                    <ul class="mb-0">
                        <li>Defina períodos amplos (ex: 09:00 - 17:00) para cada dia de atendimento.</li>
                        <li>O sistema dividirá automaticamente em slots de 1 hora para agendamentos.</li>
                        <li>Você pode adicionar múltiplos períodos para o mesmo dia (ex: manhã e tarde).</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
