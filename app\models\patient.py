"""
Patient Model - Single Responsibility Principle
Entidade de domínio responsável pela representação do paciente
"""

from datetime import date, datetime
from app import db
from .base_model import BaseModel


class Patient(BaseModel):
    """
    Entidade Patient - representa um paciente
    Responsabilidades:
    - Dados pessoais do paciente
    - Histórico médico básico
    - Relacionamentos com agendamentos
    """
    __tablename__ = 'patients'
    
    user_id = db.<PERSON>umn(db.Integer, db.ForeignKey('users.id'), nullable=True, unique=True)
    professional_id = db.Column(db.Integer, db.<PERSON>ey('professionals.id'), nullable=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    date_of_birth = db.<PERSON>umn(db.Date)
    address = db.Column(db.Text)
    emergency_contact = db.Column(db.String(100))
    emergency_phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    medical_history = db.Column(db.Text)
    allergies = db.Column(db.Text)
    medications = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # Relacionamentos
    appointments = db.relationship('Appointment', backref='patient', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        """Inicializa o paciente com validações"""
        super().__init__(**kwargs)
        self._validate_required_fields()
    
    def _validate_required_fields(self):
        """Valida campos obrigatórios"""
        if not self.name or len(self.name.strip()) < 2:
            raise ValueError("Patient name is required and must be at least 2 characters")
    
    def calculate_age(self) -> int:
        """Calcula a idade do paciente"""
        if not self.date_of_birth:
            return None
        
        today = date.today()
        age = today.year - self.date_of_birth.year
        
        # Ajusta se ainda não fez aniversário este ano
        if today.month < self.date_of_birth.month or \
           (today.month == self.date_of_birth.month and today.day < self.date_of_birth.day):
            age -= 1
            
        return age
    
    def get_appointments_count(self) -> int:
        """Retorna o número total de agendamentos"""
        return self.appointments.count()
    
    def get_upcoming_appointments(self):
        """Retorna agendamentos futuros"""
        today = date.today()
        return self.appointments.filter(
            db.and_(
                db.func.date(self.appointments.c.appointment_date) >= today,
                self.appointments.c.status != 'cancelled'
            )
        ).all()
    
    def get_past_appointments(self):
        """Retorna agendamentos passados"""
        today = date.today()
        return self.appointments.filter(
            db.func.date(self.appointments.c.appointment_date) < today
        ).all()
    
    def get_cancelled_appointments(self):
        """Retorna agendamentos cancelados"""
        return self.appointments.filter_by(status='cancelled').all()
    
    def has_upcoming_appointments(self) -> bool:
        """Verifica se tem agendamentos futuros"""
        return len(self.get_upcoming_appointments()) > 0
    
    def get_last_appointment(self):
        """Retorna o último agendamento"""
        return self.appointments.order_by(
            self.appointments.c.appointment_date.desc(),
            self.appointments.c.start_time.desc()
        ).first()
    
    def update_contact_info(self, email: str = None, phone: str = None, address: str = None):
        """Atualiza informações de contato"""
        if email:
            self.email = email
        if phone:
            self.phone = phone
        if address:
            self.address = address
        self.save()
    
    def update_emergency_contact(self, contact_name: str, contact_phone: str):
        """Atualiza contato de emergência"""
        self.emergency_contact = contact_name
        self.emergency_phone = contact_phone
        self.save()
    
    def update_medical_info(self, medical_history: str = None, allergies: str = None, medications: str = None):
        """Atualiza informações médicas"""
        if medical_history is not None:
            self.medical_history = medical_history
        if allergies is not None:
            self.allergies = allergies
        if medications is not None:
            self.medications = medications
        self.save()
    
    def add_notes(self, notes: str):
        """Adiciona notas ao paciente"""
        if self.notes:
            self.notes += f"\n\n[{datetime.now().strftime('%Y-%m-%d %H:%M')}] {notes}"
        else:
            self.notes = f"[{datetime.now().strftime('%Y-%m-%d %H:%M')}] {notes}"
        self.save()
    
    def activate(self):
        """Ativa o paciente"""
        self.is_active = True
        self.save()
    
    def deactivate(self):
        """Desativa o paciente"""
        self.is_active = False
        self.save()
    
    def get_statistics(self) -> dict:
        """Retorna estatísticas do paciente"""
        return {
            'age': self.calculate_age(),
            'total_appointments': self.get_appointments_count(),
            'upcoming_appointments': len(self.get_upcoming_appointments()),
            'past_appointments': len(self.get_past_appointments()),
            'cancelled_appointments': len(self.get_cancelled_appointments()),
            'has_upcoming': self.has_upcoming_appointments(),
            'is_active': self.is_active
        }
    
    def to_dict(self):
        """Converte para dicionário incluindo estatísticas"""
        data = super().to_dict()
        data['age'] = self.calculate_age()
        data['statistics'] = self.get_statistics()
        return data
    
    def __repr__(self):
        return f'<Patient {self.name}>'
