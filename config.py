import os
from datetime import timedelta
from dotenv import load_dotenv

# Carregar variáveis de ambiente do arquivo .env
load_dotenv()

class Config:
    # Secret key for session management and CSRF protection
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-for-development-only'

    # Database configuration - SQLite for development
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///gestao_terapia.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    # SQLite doesn't need these options
    SQLALCHEMY_ENGINE_OPTIONS = {}

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)

    # Google OAuth configuration
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
    GOOGLE_REDIRECT_URI = os.environ.get('GOOGLE_REDIRECT_URI') or 'http://localhost:5000/oauth2callback'
    GOOGLE_PROJECT_ID = os.environ.get('GOOGLE_PROJECT_ID')

    # PostgreSQL specific configurations
    POSTGRES_DB = os.environ.get('POSTGRES_DB') or 'gestao_terapia'
    POSTGRES_USER = os.environ.get('POSTGRES_USER') or 'postgres'
    POSTGRES_PASSWORD = os.environ.get('POSTGRES_PASSWORD') or 'postgres123'
    POSTGRES_HOST = os.environ.get('POSTGRES_HOST') or 'localhost'
    POSTGRES_PORT = os.environ.get('POSTGRES_PORT') or '5432'
