{% extends "base.html" %}

{% block title %}Edit Schedule - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Editar Horá<PERSON> na Agenda</h4>
                <a href="{{ url_for('main.professional_schedule') }}" class="btn btn-outline-secondary btn-sm">Voltar</a>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.day_of_week.label(class="form-label") }}
                        {{ form.day_of_week(class="form-select") }}
                        {% for error in form.day_of_week.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.start_time.label(class="form-label") }}
                        {{ form.start_time(class="form-control") }}
                        {% for error in form.start_time.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <small class="form-text text-muted">Formato: HH:MM (24 horas)</small>
                    </div>
                    <div class="mb-3">
                        {{ form.end_time.label(class="form-label") }}
                        {{ form.end_time(class="form-control") }}
                        {% for error in form.end_time.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                        <small class="form-text text-muted">Formato: HH:MM (24 horas)</small>
                    </div>
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
