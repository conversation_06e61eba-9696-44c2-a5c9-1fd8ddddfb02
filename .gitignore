# Arquivos de ambiente e credenciais
.env
*.env
.env.*
client_secret*.json
token.pickle
credentials.json
*.pem
*.key
*.crt

# Banco de dados SQLite
*.db
*.sqlite
*.sqlite3
instance/

# Arquivos de cache Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Ambiente virtual
venv/
env/
ENV/
.venv/
.env/
.ENV/

# Arquivos de log
*.log
logs/
log/

# Arquivos temporários
*.tmp
*.temp
*.swp
*~
.DS_Store
Thumbs.db

# Arquivos de IDE e editores
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.pydevproject
.settings/
*.code-workspace

# Arquivos de configuração local
local_settings.py
settings_local.py
config_local.py

# Arquivos de teste e cobertura
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.coverage.*

# Arquivos de documentação
docs/_build/
site/

# Arquivos de dependências
node_modules/
bower_components/

# Arquivos de compilação
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.exe
*.out
*.app

# Arquivos específicos do Flask
instance/
.webassets-cache

# Arquivos específicos do Django
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Arquivos específicos do Jupyter Notebook
.ipynb_checkpoints

# Arquivos específicos do pyenv
.python-version

# Arquivos específicos do celery
celerybeat-schedule
celerybeat.pid

# Arquivos específicos do SageMath
*.sage.py

# Arquivos específicos do Spyder
.spyderproject
.spyproject

# Arquivos específicos do Rope
.ropeproject

# Arquivos específicos do mkdocs
/site

# Arquivos específicos do mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Arquivos específicos do Pyre
.pyre/

# Arquivos específicos do Google OAuth
google_credentials/
